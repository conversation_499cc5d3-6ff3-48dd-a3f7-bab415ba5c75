import logging

from aiohttp import ClientConnectorError


async def download_image(session, url, config, seed, timestamp, idx):
    """Downloads the image asynchronously and saves it locally."""
    attempts = config.get("retry_attempts", 3)
    output_folder = config["output_directory"]

    while attempts > 0:
        try:
            async with session.get(url) as response:
                if response.status == 200:
                    filename = f"{output_folder}/{timestamp}_seed_{seed}_image_no_{idx}.{config['image_format']}"
                    with open(filename, "wb") as f:
                        f.write(await response.read())
                    logging.info(f"Downloaded and saved {filename}")
                    print(f"Downloaded and saved {filename}")
                    return
                else:
                    logging.warning(f"Failed to download image {idx}. Status code: {response.status}")
                    print(f"Failed to download image {idx}. Status code: {response.status}")
        except ClientConnectorError as e:
            logging.warning(f"Network error occurred: {e}")

        attempts -= 1
        if attempts > 0:
            logging.info(f"Retrying... ({config['retry_attempts'] - attempts}/{config['retry_attempts']})")
            print(f"Retrying... ({config['retry_attempts'] - attempts}/{config['retry_attempts']})")

    logging.error(f"Failed to download image {idx} after {config['retry_attempts']} attempts.")
