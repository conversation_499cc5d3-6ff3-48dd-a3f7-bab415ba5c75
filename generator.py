"""
FLUX.1 Kontext Pro API Client
A comprehensive client for interacting with the FLUX.1 Kontext Pro text-to-image API.
"""

import asyncio
import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum

import fal_client
from aiohttp import ClientSession


class SafetyTolerance(Enum):
    """Safety tolerance levels for image generation."""
    VERY_STRICT = "1"
    STRICT = "2"
    MODERATE = "3"
    PERMISSIVE = "4"
    VERY_PERMISSIVE = "5"
    MAXIMUM_PERMISSIVE = "6"


class OutputFormat(Enum):
    """Output format options for generated images."""
    JPEG = "jpeg"
    PNG = "png"


class AspectRatio(Enum):
    """Aspect ratio options for generated images."""
    ULTRAWIDE = "21:9"
    WIDESCREEN = "16:9"
    STANDARD = "4:3"
    PHOTO = "3:2"
    SQUARE = "1:1"
    PORTRAIT_PHOTO = "2:3"
    PORTRAIT_STANDARD = "3:4"
    PORTRAIT_WIDESCREEN = "9:16"
    PORTRAIT_ULTRAWIDE = "9:21"


@dataclass
class GenerationRequest:
    """Data class for image generation requests."""
    prompt: str
    seed: Optional[int] = None
    guidance_scale: float = 3.5
    num_images: int = 1
    safety_tolerance: SafetyTolerance = SafetyTolerance.STRICT
    output_format: OutputFormat = OutputFormat.JPEG
    aspect_ratio: AspectRatio = AspectRatio.SQUARE
    sync_mode: bool = False


@dataclass
class GenerationResult:
    """Data class for image generation results."""
    images: List[Dict[str, Any]]
    prompt: str
    seed: int
    has_nsfw_concepts: List[bool]
    timings: Dict[str, Any]
    request_id: Optional[str] = None


@dataclass
class EditingRequest:
    """Data class for image editing requests."""
    prompt: str
    image_url: str
    seed: Optional[int] = None
    guidance_scale: float = 3.5
    num_images: int = 1
    safety_tolerance: SafetyTolerance = SafetyTolerance.MAXIMUM_PERMISSIVE
    output_format: OutputFormat = OutputFormat.PNG
    aspect_ratio: Optional[AspectRatio] = None
    sync_mode: bool = False


@dataclass
class EditingResult:
    """Data class for image editing results."""
    images: List[Dict[str, Any]]
    prompt: str
    seed: int
    has_nsfw_concepts: List[bool]
    timings: Dict[str, Any]
    request_id: Optional[str] = None
    original_image_url: Optional[str] = None


class FluxKontextClient:
    """
    Client for FLUX.1 Kontext Pro API with comprehensive error handling,
    progress monitoring, and result management. Supports both text-to-image
    generation and image editing capabilities.
    """

    MODEL_ENDPOINT = "fal-ai/flux-pro/kontext/text-to-image"
    EDITING_ENDPOINT = "fal-ai/flux-pro/kontext"

    def __init__(self, api_key: Optional[str] = None,
                 progress_callback: Optional[Callable[[str], None]] = None,
                 log_callback: Optional[Callable[[str], None]] = None):
        """
        Initialize the FLUX Kontext client.

        Args:
            api_key: FAL API key. If None, will use FAL_KEY environment variable.
            progress_callback: Callback function for progress updates.
            log_callback: Callback function for log messages.
        """
        self.api_key = api_key or os.getenv("FAL_KEY")
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.logger = logging.getLogger(__name__)

        if not self.api_key:
            raise ValueError("FAL_KEY must be provided either as parameter or environment variable")

        # Set the API key for fal_client
        os.environ["FAL_KEY"] = self.api_key

    def _log(self, message: str) -> None:
        """Log a message using the callback or logger."""
        if self.log_callback:
            self.log_callback(message)
        self.logger.info(message)

    def _update_progress(self, message: str) -> None:
        """Update progress using the callback."""
        if self.progress_callback:
            self.progress_callback(message)
        self._log(f"Progress: {message}")

    def _validate_request(self, request: GenerationRequest) -> None:
        """Validate the generation request parameters."""
        if not request.prompt or not request.prompt.strip():
            raise ValueError("Prompt cannot be empty")

        if request.guidance_scale < 0 or request.guidance_scale > 20:
            raise ValueError("Guidance scale must be between 0 and 20")

        if request.num_images < 1 or request.num_images > 10:
            raise ValueError("Number of images must be between 1 and 10")

        if request.seed is not None and (request.seed < 0 or request.seed > 2**32 - 1):
            raise ValueError("Seed must be between 0 and 2^32 - 1")

    def _prepare_arguments(self, request: GenerationRequest) -> Dict[str, Any]:
        """Prepare API arguments from the generation request."""
        arguments = {
            "prompt": request.prompt.strip(),
            "guidance_scale": request.guidance_scale,
            "num_images": request.num_images,
            "safety_tolerance": request.safety_tolerance.value,
            "output_format": request.output_format.value,
            "aspect_ratio": request.aspect_ratio.value,
            "sync_mode": request.sync_mode
        }

        if request.seed is not None:
            arguments["seed"] = request.seed

        return arguments

    async def generate_async(self, request: GenerationRequest) -> GenerationResult:
        """
        Generate images asynchronously using the FLUX.1 Kontext Pro API.

        Args:
            request: Generation request parameters.

        Returns:
            GenerationResult containing the generated images and metadata.

        Raises:
            ValueError: If request parameters are invalid.
            Exception: If API request fails.
        """
        self._validate_request(request)
        arguments = self._prepare_arguments(request)

        self._log(f"Starting image generation with prompt: '{request.prompt[:50]}...'")
        self._update_progress("Submitting request to FLUX.1 Kontext Pro...")

        try:
            # Submit the request
            handler = await fal_client.submit_async(
                self.MODEL_ENDPOINT,
                arguments=arguments
            )

            request_id = handler.request_id
            self._log(f"Request submitted with ID: {request_id}")
            self._update_progress("Request submitted, waiting for processing...")

            # Monitor progress
            log_index = 0
            async for event in handler.iter_events(with_logs=True):
                if isinstance(event, fal_client.InProgress):
                    new_logs = event.logs[log_index:]
                    for log in new_logs:
                        self._log(f"API: {log['message']}")
                    log_index = len(event.logs)
                    self._update_progress("Processing image generation...")

            # Get the result
            self._update_progress("Retrieving generated images...")
            result = await handler.get()

            # Process the result
            generation_result = GenerationResult(
                images=result.get("images", []),
                prompt=result.get("prompt", request.prompt),
                seed=result.get("seed", request.seed or 0),
                has_nsfw_concepts=result.get("has_nsfw_concepts", []),
                timings=result.get("timings", {}),
                request_id=request_id
            )

            self._log(f"Generation completed successfully. Generated {len(generation_result.images)} images.")
            self._update_progress("Generation completed successfully!")

            return generation_result

        except fal_client.auth.MissingCredentialsError as e:
            error_msg = "Missing or invalid API credentials. Please check your FAL_KEY."
            self._log(f"Authentication error: {error_msg}")
            raise Exception(error_msg) from e

        except Exception as e:
            error_msg = f"Image generation failed: {str(e)}"
            self._log(f"Generation error: {error_msg}")
            raise Exception(error_msg) from e

    def generate(self, request: GenerationRequest) -> GenerationResult:
        """
        Generate images synchronously using the FLUX.1 Kontext Pro model.

        Args:
            request: Generation request parameters.

        Returns:
            GenerationResult containing the generated images and metadata.
        """
        loop = None
        try:
            # Check if there's already an event loop running
            try:
                loop = asyncio.get_running_loop()
                # If we're in an existing loop, we can't use run_until_complete
                import concurrent.futures
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.generate_async(request))
                    finally:
                        # Wait for all tasks to complete before closing
                        pending = asyncio.all_tasks(new_loop)
                        if pending:
                            new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
                    
            except RuntimeError:
                # No event loop running, we can create our own
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.generate_async(request))
                
                # Wait for all pending tasks to complete
                pending = asyncio.all_tasks(loop)
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                
                return result
                
        except Exception as e:
            raise e
        finally:
            if loop and not loop.is_closed():
                loop.close()

    async def check_status_async(self, request_id: str) -> Dict[str, Any]:
        """
        Check the status of a submitted request asynchronously.

        Args:
            request_id: The request ID to check.

        Returns:
            Status information dictionary.
        """
        try:
            status = await fal_client.status_async(self.MODEL_ENDPOINT, request_id, with_logs=True)
            return status
        except Exception as e:
            error_msg = f"Failed to check status for request {request_id}: {str(e)}"
            self._log(error_msg)
            raise Exception(error_msg) from e

    def check_status(self, request_id: str) -> Dict[str, Any]:
        """
        Check the status of a submitted request synchronously.

        Args:
            request_id: The request ID to check.

        Returns:
            Status information dictionary.
        """
        loop = None
        try:
            # Check if there's already an event loop running
            try:
                loop = asyncio.get_running_loop()
                # If we're in an existing loop, use thread pool
                import concurrent.futures
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.check_status_async(request_id))
                    finally:
                        pending = asyncio.all_tasks(new_loop)
                        if pending:
                            new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
                    
            except RuntimeError:
                # No event loop running, we can create our own
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.check_status_async(request_id))
                
                # Wait for all pending tasks to complete
                pending = asyncio.all_tasks(loop)
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                
                return result
                
        except Exception as e:
            raise e
        finally:
            if loop and not loop.is_closed():
                loop.close()

    @staticmethod
    def create_request(prompt: str, **kwargs) -> GenerationRequest:
        """
        Create a generation request with the given parameters.

        Args:
            prompt: The text prompt for image generation.
            **kwargs: Additional parameters for the request.

        Returns:
            GenerationRequest object.
        """
        # Convert string enums to enum objects if needed
        if 'safety_tolerance' in kwargs and isinstance(kwargs['safety_tolerance'], str):
            kwargs['safety_tolerance'] = SafetyTolerance(kwargs['safety_tolerance'])

        if 'output_format' in kwargs and isinstance(kwargs['output_format'], str):
            kwargs['output_format'] = OutputFormat(kwargs['output_format'])

        if 'aspect_ratio' in kwargs and isinstance(kwargs['aspect_ratio'], str):
            kwargs['aspect_ratio'] = AspectRatio(kwargs['aspect_ratio'])

        return GenerationRequest(prompt=prompt, **kwargs)

    async def upload_image_async(self, image_path: str) -> str:
        """
        Upload an image file to FAL storage for use in editing requests.

        Args:
            image_path: Path to the local image file.

        Returns:
            URL of the uploaded image.

        Raises:
            Exception: If upload fails.
        """
        try:
            self._log(f"Uploading image: {image_path}")
            self._update_progress("Uploading image...")

            # Upload file using fal_client
            url = await fal_client.upload_file_async(image_path)

            self._log(f"Image uploaded successfully: {url}")
            self._update_progress("Image uploaded successfully")

            return url

        except Exception as e:
            error_msg = f"Failed to upload image: {str(e)}"
            self._log(f"Upload error: {error_msg}")
            raise Exception(error_msg) from e

    def upload_image(self, image_path: str) -> str:
        """
        Upload an image file synchronously.

        Args:
            image_path: Path to the local image file.

        Returns:
            URL of the uploaded image.
        """
        loop = None
        try:
            # Check if there's already an event loop running
            try:
                loop = asyncio.get_running_loop()
                # If we're in an existing loop, use thread pool
                import concurrent.futures
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.upload_image_async(image_path))
                    finally:
                        pending = asyncio.all_tasks(new_loop)
                        if pending:
                            new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
                    
            except RuntimeError:
                # No event loop running, we can create our own
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.upload_image_async(image_path))
                
                # Wait for all pending tasks to complete
                pending = asyncio.all_tasks(loop)
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                
                return result
                
        except Exception as e:
            raise e
        finally:
            if loop and not loop.is_closed():
                loop.close()

    def _validate_editing_request(self, request: EditingRequest) -> None:
        """Validate the editing request parameters."""
        if not request.prompt or not request.prompt.strip():
            raise ValueError("Editing prompt cannot be empty")

        if not request.image_url or not request.image_url.strip():
            raise ValueError("Image URL cannot be empty")

        if request.guidance_scale < 0 or request.guidance_scale > 20:
            raise ValueError("Guidance scale must be between 0 and 20")

        if request.num_images < 1 or request.num_images > 10:
            raise ValueError("Number of images must be between 1 and 10")

        if request.seed is not None and (request.seed < 0 or request.seed > 2**32 - 1):
            raise ValueError("Seed must be between 0 and 2^32 - 1")

    def _prepare_editing_arguments(self, request: EditingRequest) -> Dict[str, Any]:
        """Prepare API arguments from the editing request."""
        arguments = {
            "prompt": request.prompt.strip(),
            "image_url": request.image_url,
            "guidance_scale": request.guidance_scale,
            "num_images": request.num_images,
            "safety_tolerance": request.safety_tolerance.value,
            "output_format": request.output_format.value,
            "sync_mode": request.sync_mode
        }

        if request.seed is not None:
            arguments["seed"] = request.seed

        if request.aspect_ratio is not None:
            arguments["aspect_ratio"] = request.aspect_ratio.value

        return arguments

    async def edit_image_async(self, request: EditingRequest) -> EditingResult:
        """
        Edit an image asynchronously using the FLUX.1 Kontext Pro editing model.

        Args:
            request: Editing request parameters.

        Returns:
            EditingResult containing the edited images and metadata.

        Raises:
            ValueError: If request parameters are invalid.
            Exception: If API request fails.
        """
        self._validate_editing_request(request)
        arguments = self._prepare_editing_arguments(request)

        self._log(f"Starting image editing with prompt: '{request.prompt[:50]}...'")
        self._update_progress("Submitting editing request to FLUX.1 Kontext Pro...")

        try:
            # Submit the editing request
            handler = await fal_client.submit_async(
                self.EDITING_ENDPOINT,
                arguments=arguments
            )

            request_id = handler.request_id
            self._log(f"Editing request submitted with ID: {request_id}")
            self._update_progress("Editing request submitted, waiting for processing...")

            # Monitor progress
            log_index = 0
            async for event in handler.iter_events(with_logs=True):
                if isinstance(event, fal_client.InProgress):
                    new_logs = event.logs[log_index:]
                    for log in new_logs:
                        self._log(f"API: {log['message']}")
                    log_index = len(event.logs)
                    self._update_progress("Processing image editing...")

            # Get the result
            self._update_progress("Retrieving edited images...")
            result = await handler.get()

            # Process the result
            editing_result = EditingResult(
                images=result.get("images", []),
                prompt=result.get("prompt", request.prompt),
                seed=result.get("seed", request.seed or 0),
                has_nsfw_concepts=result.get("has_nsfw_concepts", []),
                timings=result.get("timings", {}),
                request_id=request_id,
                original_image_url=request.image_url
            )

            self._log(f"Image editing completed successfully. Generated {len(editing_result.images)} edited images.")
            self._update_progress("Image editing completed successfully!")

            return editing_result

        except fal_client.auth.MissingCredentialsError as e:
            error_msg = "Missing or invalid API credentials. Please check your FAL_KEY."
            self._log(f"Authentication error: {error_msg}")
            raise Exception(error_msg) from e

        except Exception as e:
            error_msg = f"Image editing failed: {str(e)}"
            self._log(f"Editing error: {error_msg}")
            raise Exception(error_msg) from e

    def edit_image(self, request: EditingRequest) -> EditingResult:
        """
        Edit an image synchronously using the FLUX.1 Kontext Pro editing model.

        Args:
            request: Editing request parameters.

        Returns:
            EditingResult containing the edited images and metadata.
        """
        loop = None
        try:
            # Check if there's already an event loop running
            try:
                loop = asyncio.get_running_loop()
                # If we're in an existing loop, we can't use run_until_complete
                # Instead, we need to create a new thread or handle differently
                import concurrent.futures
                import threading
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.edit_image_async(request))
                    finally:
                        # Wait for all tasks to complete before closing
                        pending = asyncio.all_tasks(new_loop)
                        if pending:
                            new_loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
                    
            except RuntimeError:
                # No event loop running, we can create our own
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.edit_image_async(request))
                
                # Wait for all pending tasks to complete
                pending = asyncio.all_tasks(loop)
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                
                return result
                
        except Exception as e:
            raise e
        finally:
            if loop and not loop.is_closed():
                loop.close()

    @staticmethod
    def create_editing_request(prompt: str, image_url: str, **kwargs) -> EditingRequest:
        """
        Create an editing request with the given parameters.

        Args:
            prompt: The editing prompt describing what to change.
            image_url: URL of the image to edit.
            **kwargs: Additional parameters for the request.

        Returns:
            EditingRequest object.
        """
        # Convert string enums to enum objects if needed
        if 'safety_tolerance' in kwargs and isinstance(kwargs['safety_tolerance'], str):
            kwargs['safety_tolerance'] = SafetyTolerance(kwargs['safety_tolerance'])

        if 'output_format' in kwargs and isinstance(kwargs['output_format'], str):
            kwargs['output_format'] = OutputFormat(kwargs['output_format'])

        if 'aspect_ratio' in kwargs and isinstance(kwargs['aspect_ratio'], str):
            kwargs['aspect_ratio'] = AspectRatio(kwargs['aspect_ratio'])

        return EditingRequest(prompt=prompt, image_url=image_url, **kwargs)


# Legacy compatibility functions for existing GUI
def run_asyncio_task(prompt, config, settings):
    """
    Legacy wrapper function for backward compatibility with existing GUI.
    This will be replaced when the new GUI is implemented.
    """
    try:
        # Convert old settings format to new GenerationRequest
        request = GenerationRequest(
            prompt=prompt,
            seed=settings.get("seed"),
            guidance_scale=settings.get("guidance_scale", 3.5),
            num_images=settings.get("num_images", 1),
            safety_tolerance=SafetyTolerance(settings.get("safety_tolerance", "2")),
            output_format=OutputFormat(settings.get("output_format", "jpeg")),
            aspect_ratio=AspectRatio(settings.get("aspect_ratio", "1:1"))
        )

        # Create client with print callbacks for compatibility
        def log_callback(message):
            print(message)

        def progress_callback(message):
            print(f"Progress: {message}")

        client = FluxKontextClient(
            log_callback=log_callback,
            progress_callback=progress_callback
        )

        # Generate images
        result = client.generate(request)

        # Handle image downloading (simplified for now)
        if result.images:
            print(f"Generated {len(result.images)} images successfully!")
            for idx, image in enumerate(result.images, 1):
                print(f"Image {idx}: {image.get('url', 'No URL available')}")
        else:
            print("No images were generated.")

        return result

    except Exception as e:
        logging.error(f"Generation failed: {str(e)}")
        print(f"Generation failed: {str(e)}")
        return None