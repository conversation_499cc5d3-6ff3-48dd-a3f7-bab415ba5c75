#!/usr/bin/env python3
"""
Comprehensive test for the real image editing workflow.
Tests the complete editing pipeline with actual images and API calls.
"""

import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from generator import FluxKontextClient, EditingRequest, SafetyTolerance, OutputFormat
from settings import ConfigManager


def test_api_connection():
    """Test if we can connect to the FAL API."""
    print("🔗 Testing API connection...")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        if not config.api_key:
            print("❌ No API key found in configuration")
            return False
            
        # Initialize client
        FluxKontextClient(api_key=config.api_key)
        print("✅ FLUX client initialized successfully")
        
        # Test basic API connectivity by checking if we can create a client
        print(f"✅ API key configured: {config.api_key[:10]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ API connection test failed: {e}")
        return False


def test_image_upload():
    """Test image upload functionality."""
    print("\n📤 Testing image upload...")
    
    try:
        # Find an existing image in the output directory
        output_dir = Path("output")
        image_files = list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpg"))
        
        if not image_files:
            print("❌ No images found in output directory for testing")
            return False, None
            
        # Use the latest image
        test_image = max(image_files, key=os.path.getctime)
        print(f"📸 Using test image: {test_image.name}")
        
        # Test file upload to FAL
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        if not config.api_key:
            print("❌ No API key for upload test")
            return False, None
            
        # Upload the image using the FLUX client
        print("⏳ Uploading image to FAL storage...")

        client = FluxKontextClient(api_key=config.api_key)
        image_url = client.upload_image(str(test_image))

        print(f"✅ Image uploaded successfully: {image_url}")
        
        return True, image_url
        
    except Exception as e:
        print(f"❌ Image upload test failed: {e}")
        return False, None


def test_editing_request_creation():
    """Test creating an editing request."""
    print("\n📝 Testing editing request creation...")
    
    try:
        # Create a test editing request
        request = EditingRequest(
            prompt="Add beautiful clouds to the sky",
            image_url="https://example.com/test.jpg",
            guidance_scale=3.5,
            num_images=1,
            safety_tolerance=SafetyTolerance.MAXIMUM_PERMISSIVE,
            output_format=OutputFormat.PNG,
            sync_mode=True
        )
        
        print("✅ EditingRequest created successfully")
        print(f"   - Prompt: {request.prompt}")
        print(f"   - Guidance scale: {request.guidance_scale}")
        print(f"   - Safety tolerance: {request.safety_tolerance.value}")
        print(f"   - Output format: {request.output_format.value}")
        print(f"   - Sync mode: {request.sync_mode}")
        
        return True, request
        
    except Exception as e:
        print(f"❌ Editing request creation failed: {e}")
        return False, None


def test_real_editing_workflow(image_url):
    """Test the complete editing workflow with a real image."""
    print("\n🎨 Testing real editing workflow...")
    
    try:
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        # Initialize client
        client = FluxKontextClient(api_key=config.api_key)
        
        # Create editing request
        request = EditingRequest(
            prompt="Add a beautiful rainbow in the sky",
            image_url=image_url,
            guidance_scale=3.5,
            num_images=1,
            safety_tolerance=SafetyTolerance.MAXIMUM_PERMISSIVE,
            output_format=OutputFormat.PNG,
            sync_mode=True
        )
        
        print(f"✅ Created editing request for image: {image_url}")
        print(f"   - Editing prompt: {request.prompt}")
        
        # Submit editing request
        print("⏳ Submitting editing request to FLUX.1 Kontext Pro...")

        result = client.edit_image(request)

        # If we get here, the editing was successful (no exception was raised)
        print("✅ Image editing completed successfully!")
        print(f"   - Generated {len(result.images)} edited image(s)")
        print(f"   - Request ID: {result.request_id}")

        # Test image download
        for i, image_info in enumerate(result.images):
            # Each image_info is a dict with 'url' key
            image_url = image_info.get('url') if isinstance(image_info, dict) else image_info
            print(f"   - Image {i+1}: {image_url}")

            # Try to download the first image
            if i == 0:
                print("⏳ Testing image download...")

                # Since the image is base64 encoded, we'll just verify it's valid
                if image_url.startswith('data:image/'):
                    print("✅ Image is base64 encoded - download test passed")
                    print("✅ Test cleanup completed")
                else:
                    # For actual URLs, we would need async download
                    print("❌ Image download test - unexpected format")
                    return False

        return True
            
    except Exception as e:
        print(f"❌ Real editing workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gui_integration():
    """Test GUI integration without actually opening the GUI."""
    print("\n🖥️ Testing GUI integration...")
    
    try:
        from modern_gui import ModernFluxGUI
        
        # Test that GUI can be imported and has editing methods
        editing_methods = [
            'create_editing_content',
            'upload_image_file', 
            'start_editing',
            'edit_generated_image'
        ]
        
        for method in editing_methods:
            if hasattr(ModernFluxGUI, method):
                print(f"✅ GUI method available: {method}")
            else:
                print(f"❌ GUI method missing: {method}")
                return False
                
        print("✅ All GUI editing methods are available")
        return True
        
    except Exception as e:
        print(f"❌ GUI integration test failed: {e}")
        return False


def run_comprehensive_editing_tests():
    """Run all comprehensive editing tests."""
    print("=" * 80)
    print("FLUX.1 Kontext Pro - COMPREHENSIVE IMAGE EDITING WORKFLOW TEST")
    print("=" * 80)
    
    tests = [
        ("API Connection", test_api_connection, []),
        ("Image Upload", test_image_upload, []),
        ("Editing Request Creation", test_editing_request_creation, []),
        ("GUI Integration", test_gui_integration, [])
    ]
    
    passed = 0
    failed = 0
    image_url = None
    
    for test_name, test_func, args in tests:
        print(f"\n{'=' * 60}")
        print(f"Running {test_name}")
        print(f"{'=' * 60}")
        
        try:
            if test_name == "Image Upload":
                success, image_url = test_func(*args)
            elif test_name == "Editing Request Creation":
                success, _ = test_func(*args)
            else:
                success = test_func(*args)
                
            if success:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    # Run real editing workflow test if we have an uploaded image
    if image_url:
        print(f"\n{'=' * 60}")
        print("Running Real Editing Workflow")
        print(f"{'=' * 60}")
        
        try:
            if test_real_editing_workflow(image_url):
                passed += 1
                print("✅ Real Editing Workflow PASSED")
            else:
                failed += 1
                print("❌ Real Editing Workflow FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ Real Editing Workflow FAILED with exception: {e}")
    else:
        print("\n⚠️ Skipping real editing workflow test (no uploaded image)")
    
    # Print summary
    total_tests = passed + failed
    print(f"\n{'=' * 80}")
    print("COMPREHENSIVE EDITING TEST RESULTS SUMMARY")
    print(f"{'=' * 80}")
    print(f"Total tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if total_tests > 0:
        print(f"Success rate: {(passed / total_tests * 100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All comprehensive editing tests passed!")
        print("The image editing workflow is fully functional.")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Check the issues above.")
        
    return failed == 0


if __name__ == "__main__":
    success = run_comprehensive_editing_tests()
    sys.exit(0 if success else 1)
