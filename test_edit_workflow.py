#!/usr/bin/env python3
"""
Simple test script to verify drag-and-drop and edit functionality.
"""

import os
import sys
import tkinter as tk
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """Test basic functionality without full GUI."""
    print("Testing basic imports and functionality...")
    
    try:
        # Test tkinterdnd2 import
        try:
            import tkinterdnd2 as TkinterDnD
            print("✓ tkinterdnd2 is available")
            drag_drop_available = True
        except ImportError:
            print("⚠ tkinterdnd2 not available - drag-and-drop will not work")
            print("  Install with: pip install tkinterdnd2")
            drag_drop_available = False
        
        # Test generator imports
        from generator import FluxKontextClient, GenerationRequest, EditingRequest
        print("✓ Generator classes imported successfully")

        # Test GUI import
        from modern_gui import ModernFluxGUI
        print("✓ ModernFluxGUI imported successfully")

        # Test FLUX client initialization
        try:
            client = FluxKontextClient()
            print("✓ FLUX client initialized successfully")
        except Exception as e:
            print(f"⚠ FLUX client initialization failed: {e}")
            print("  Check your API key in settings")
            client = None
        
        # Check for existing images in output directory
        output_dir = Path("output")
        if output_dir.exists():
            image_files = list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpg"))
            if image_files:
                print(f"✓ Found {len(image_files)} existing images in output directory")
                latest_image = max(image_files, key=os.path.getctime)
                print(f"  Latest image: {latest_image.name}")
            else:
                print("⚠ No images found in output directory")
                print("  Generate some images first to test editing")
        else:
            print("⚠ Output directory not found")
        
        print("\n" + "="*50)
        print("SUMMARY:")
        print(f"- Drag-and-drop available: {drag_drop_available}")
        print(f"- FLUX client available: {client is not None}")
        print(f"- Images available for testing: {len(image_files) if 'image_files' in locals() else 0}")
        
        if not drag_drop_available:
            print("\nTo enable drag-and-drop:")
            print("1. Install tkinterdnd2: pip install tkinterdnd2")
            print("2. Restart the application")
        
        if client is None:
            print("\nTo enable image generation/editing:")
            print("1. Set your FAL API key in settings")
            print("2. Restart the application")
        
        print("\nKey fixes implemented:")
        print("- Drag-and-drop now automatically uploads images")
        print("- Edit button is enabled after successful upload")
        print("- File dialog upload uses same workflow as drag-and-drop")
        print("- Proper error handling for upload failures")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("FLUX Image Editor - Workflow Test")
    print("=" * 40)
    
    success = test_basic_functionality()
    
    print("\n" + "=" * 40)
    if success:
        print("✓ Basic functionality test completed")
        print("\nTo test the full workflow:")
        print("1. Run: python modern_gui.py")
        print("2. Generate an image or drag-and-drop an existing image")
        print("3. Switch to the Editing tab")
        print("4. The edit button should be enabled after upload")
        print("5. Enter editing instructions and click Edit")
    else:
        print("✗ Basic functionality test failed")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()