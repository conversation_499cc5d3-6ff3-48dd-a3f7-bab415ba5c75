"""
Comprehensive Error Handling and Logging System for FLUX.1 Kontext Pro Image Generator
Provides robust error handling, user-friendly messages, and detailed logging functionality.
"""

import logging
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, Callable
from enum import Enum
import tkinter as tk
from tkinter import messagebox


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorType(Enum):
    """Error type enumeration for categorization."""
    API_ERROR = "API_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    FILE_ERROR = "FILE_ERROR"
    CONFIG_ERROR = "CONFIG_ERROR"
    GUI_ERROR = "GUI_ERROR"
    UNKNOWN_ERROR = "UNKNOWN_ERROR"


class FluxLogger:
    """
    Advanced logging system with multiple output targets and error categorization.
    """

    def __init__(self, log_file: str = "flux_generator.log",
                 console_output: bool = True,
                 gui_callback: Optional[Callable[[str], None]] = None):
        """
        Initialize the logger.

        Args:
            log_file: Path to the log file.
            console_output: Whether to output to console.
            gui_callback: Optional callback for GUI log updates.
        """
        self.log_file = Path(log_file)
        self.console_output = console_output
        self.gui_callback = gui_callback

        # Create logs directory if it doesn't exist
        self.log_file.parent.mkdir(exist_ok=True)

        # Setup Python logging
        self.setup_python_logging()

        # Error statistics
        self.error_counts = {error_type: 0 for error_type in ErrorType}

    def setup_python_logging(self):
        """Setup Python's built-in logging system."""
        # Create logger
        self.logger = logging.getLogger('flux_generator')
        self.logger.setLevel(logging.DEBUG)

        # Clear existing handlers
        self.logger.handlers.clear()

        # File handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # Console handler
        if self.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)

            # Console formatter (simpler)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%H:%M:%S'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)

        # File formatter (detailed)
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

    def log(self, level: LogLevel, message: str, error_type: Optional[ErrorType] = None,
            exception: Optional[Exception] = None, extra_data: Optional[Dict[str, Any]] = None):
        """
        Log a message with comprehensive details.

        Args:
            level: Log level.
            message: Log message.
            error_type: Type of error (if applicable).
            exception: Exception object (if applicable).
            extra_data: Additional data to log.
        """
        # Format the message
        formatted_message = self._format_message(message, error_type, exception, extra_data)

        # Log to Python logger
        python_level = getattr(logging, level.value)
        self.logger.log(python_level, formatted_message)

        # Update error statistics
        if error_type:
            self.error_counts[error_type] += 1

        # Send to GUI callback if available
        if self.gui_callback:
            gui_message = f"[{level.value}] {message}"
            if error_type:
                gui_message += f" ({error_type.value})"
            self.gui_callback(gui_message)

    def _format_message(self, message: str, error_type: Optional[ErrorType] = None,
                       exception: Optional[Exception] = None,
                       extra_data: Optional[Dict[str, Any]] = None) -> str:
        """Format a log message with additional context."""
        parts = [message]

        if error_type:
            parts.append(f"Type: {error_type.value}")

        if exception:
            parts.append(f"Exception: {type(exception).__name__}: {str(exception)}")
            if hasattr(exception, '__traceback__') and exception.__traceback__:
                tb_lines = traceback.format_tb(exception.__traceback__)
                parts.append(f"Traceback: {''.join(tb_lines).strip()}")

        if extra_data:
            parts.append(f"Extra: {extra_data}")

        return " | ".join(parts)

    def debug(self, message: str, **kwargs):
        """Log a debug message."""
        self.log(LogLevel.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs):
        """Log an info message."""
        self.log(LogLevel.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """Log a warning message."""
        self.log(LogLevel.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        """Log an error message."""
        self.log(LogLevel.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs):
        """Log a critical message."""
        self.log(LogLevel.CRITICAL, message, **kwargs)

    def get_error_statistics(self) -> Dict[str, int]:
        """Get error statistics."""
        return dict(self.error_counts)


class ErrorHandler:
    """
    Comprehensive error handler with user-friendly messages and recovery suggestions.
    """

    def __init__(self, logger: FluxLogger):
        """Initialize the error handler."""
        self.logger = logger

        # Error message mappings
        self.error_messages = {
            ErrorType.API_ERROR: {
                "title": "API Error",
                "message": "There was a problem communicating with the FLUX API.",
                "suggestions": [
                    "Check your internet connection",
                    "Verify your API key is correct",
                    "Try again in a few moments",
                    "Check the FLUX API status"
                ]
            },
            ErrorType.NETWORK_ERROR: {
                "title": "Network Error",
                "message": "Unable to connect to the internet or FLUX servers.",
                "suggestions": [
                    "Check your internet connection",
                    "Try disabling VPN if active",
                    "Check firewall settings",
                    "Try again later"
                ]
            },
            ErrorType.VALIDATION_ERROR: {
                "title": "Input Validation Error",
                "message": "The provided input parameters are invalid.",
                "suggestions": [
                    "Check your prompt is not empty",
                    "Verify parameter values are within valid ranges",
                    "Ensure seed is a valid number",
                    "Check aspect ratio and format selections"
                ]
            },
            ErrorType.FILE_ERROR: {
                "title": "File System Error",
                "message": "There was a problem with file operations.",
                "suggestions": [
                    "Check disk space availability",
                    "Verify write permissions to output directory",
                    "Ensure the path exists and is accessible",
                    "Try a different output location"
                ]
            },
            ErrorType.CONFIG_ERROR: {
                "title": "Configuration Error",
                "message": "There was a problem with the application configuration.",
                "suggestions": [
                    "Check the configuration file format",
                    "Verify all required settings are present",
                    "Try resetting to default settings",
                    "Check file permissions"
                ]
            },
            ErrorType.GUI_ERROR: {
                "title": "Interface Error",
                "message": "There was a problem with the user interface.",
                "suggestions": [
                    "Try restarting the application",
                    "Check if all required libraries are installed",
                    "Update your graphics drivers",
                    "Try running as administrator"
                ]
            },
            ErrorType.UNKNOWN_ERROR: {
                "title": "Unexpected Error",
                "message": "An unexpected error occurred.",
                "suggestions": [
                    "Try restarting the application",
                    "Check the log file for more details",
                    "Report this issue if it persists",
                    "Try with different parameters"
                ]
            }
        }

    def handle_error(self, error: Exception, error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
                    context: str = "", show_dialog: bool = True,
                    extra_data: Optional[Dict[str, Any]] = None) -> None:
        """
        Handle an error with logging and optional user notification.

        Args:
            error: The exception that occurred.
            error_type: Type of error for categorization.
            context: Additional context about where the error occurred.
            show_dialog: Whether to show a user dialog.
            extra_data: Additional data to include in logs.
        """
        # Log the error
        log_message = f"Error in {context}: {str(error)}" if context else str(error)
        self.logger.error(log_message, error_type=error_type, exception=error, extra_data=extra_data)

        # Show user dialog if requested
        if show_dialog:
            self._show_error_dialog(error, error_type, context)

    def _show_error_dialog(self, error: Exception, error_type: ErrorType, context: str):
        """Show a user-friendly error dialog."""
        error_info = self.error_messages.get(error_type, self.error_messages[ErrorType.UNKNOWN_ERROR])

        title = error_info["title"]
        message = error_info["message"]
        suggestions = error_info["suggestions"]

        # Create detailed message
        detailed_message = f"{message}\n\n"
        if context:
            detailed_message += f"Context: {context}\n\n"

        detailed_message += "Suggestions:\n"
        for i, suggestion in enumerate(suggestions, 1):
            detailed_message += f"{i}. {suggestion}\n"

        detailed_message += f"\nTechnical details: {type(error).__name__}: {str(error)}"

        # Show dialog
        try:
            messagebox.showerror(title, detailed_message)
        except Exception as dialog_error:
            # Fallback if GUI is not available
            print(f"Error Dialog Failed: {dialog_error}")
            print(f"Original Error: {detailed_message}")


# Global logger instance
_global_logger: Optional[FluxLogger] = None
_global_error_handler: Optional[ErrorHandler] = None


def setup_logging(log_file: str = "flux_generator.log",
                 console_output: bool = True,
                 gui_callback: Optional[Callable[[str], None]] = None) -> FluxLogger:
    """
    Setup the global logging system.

    Args:
        log_file: Path to the log file.
        console_output: Whether to output to console.
        gui_callback: Optional callback for GUI log updates.

    Returns:
        FluxLogger instance.
    """
    global _global_logger, _global_error_handler

    _global_logger = FluxLogger(log_file, console_output, gui_callback)
    _global_error_handler = ErrorHandler(_global_logger)

    # Log startup
    _global_logger.info("FLUX.1 Kontext Pro Image Generator started")

    return _global_logger


def get_logger() -> FluxLogger:
    """Get the global logger instance."""
    if _global_logger is None:
        return setup_logging()
    return _global_logger


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    if _global_error_handler is None:
        setup_logging()
    return _global_error_handler


def handle_exception(error: Exception, error_type: ErrorType = ErrorType.UNKNOWN_ERROR,
                    context: str = "", show_dialog: bool = True,
                    extra_data: Optional[Dict[str, Any]] = None) -> None:
    """
    Convenience function to handle exceptions.

    Args:
        error: The exception that occurred.
        error_type: Type of error for categorization.
        context: Additional context about where the error occurred.
        show_dialog: Whether to show a user dialog.
        extra_data: Additional data to include in logs.
    """
    error_handler = get_error_handler()
    error_handler.handle_error(error, error_type, context, show_dialog, extra_data)
