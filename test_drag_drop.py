#!/usr/bin/env python3
"""
Test script to verify drag and drop functionality.
This script will help diagnose drag and drop issues.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
    print("✅ tkinterdnd2 is available - drag and drop should work!")
except ImportError:
    DRAG_DROP_AVAILABLE = False
    print("❌ tkinterdnd2 is NOT available - drag and drop will not work")
    print("To fix this, run: pip install tkinterdnd2")

class DragDropTest(TkinterDnD.Tk if DRAG_DROP_AVAILABLE else tk.Tk):
    def __init__(self):
        super().__init__()
        
        self.title("Drag and Drop Test")
        self.geometry("400x300")
        
        # Create main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Status label
        status_text = "✅ Drag and Drop ENABLED" if DRAG_DROP_AVAILABLE else "❌ Drag and Drop DISABLED"
        status_color = "green" if DRAG_DROP_AVAILABLE else "red"
        
        status_label = tk.Label(
            main_frame,
            text=status_text,
            font=('Arial', 12, 'bold'),
            fg=status_color
        )
        status_label.pack(pady=10)
        
        # Instructions
        if DRAG_DROP_AVAILABLE:
            instructions = "Try dragging an image file into the area below:"
        else:
            instructions = "Install tkinterdnd2 to enable drag and drop:\npip install tkinterdnd2"
            
        instruction_label = tk.Label(
            main_frame,
            text=instructions,
            font=('Arial', 10),
            justify=tk.CENTER
        )
        instruction_label.pack(pady=10)
        
        # Drop area
        self.drop_area = tk.Frame(
            main_frame,
            bg='lightblue',
            relief='dashed',
            bd=2,
            height=150,
            width=300
        )
        self.drop_area.pack(pady=20, fill=tk.BOTH, expand=True)
        self.drop_area.pack_propagate(False)
        
        # Drop area label
        drop_label = tk.Label(
            self.drop_area,
            text="📁 Drop image files here" if DRAG_DROP_AVAILABLE else "📁 Drag and drop not available",
            bg='lightblue',
            font=('Arial', 11)
        )
        drop_label.pack(expand=True)
        
        # Result area
        self.result_label = tk.Label(
            main_frame,
            text="No files dropped yet",
            font=('Arial', 9),
            fg='gray'
        )
        self.result_label.pack(pady=10)
        
        # Setup drag and drop if available
        if DRAG_DROP_AVAILABLE:
            self.setup_drag_drop()
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality."""
        try:
            # Register the drop area
            self.drop_area.drop_target_register(DND_FILES)
            self.drop_area.dnd_bind('<<Drop>>', self.handle_drop)
            self.drop_area.dnd_bind('<<DragEnter>>', self.handle_drag_enter)
            self.drop_area.dnd_bind('<<DragLeave>>', self.handle_drag_leave)
            print("✅ Drag and drop setup successful")
        except Exception as e:
            print(f"❌ Drag and drop setup failed: {e}")
            messagebox.showerror("Setup Error", f"Failed to setup drag and drop: {e}")
    
    def handle_drop(self, event):
        """Handle file drop events."""
        try:
            files = self.tk.splitlist(event.data)
            if files:
                file_path = files[0]
                file_name = Path(file_path).name
                
                # Check if it's an image file
                valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.gif'}
                if Path(file_path).suffix.lower() in valid_extensions:
                    self.result_label.config(
                        text=f"✅ Image dropped: {file_name}",
                        fg='green'
                    )
                    print(f"✅ Valid image file dropped: {file_path}")
                else:
                    self.result_label.config(
                        text=f"❌ Invalid file: {file_name}",
                        fg='red'
                    )
                    print(f"❌ Invalid file type: {file_path}")
        except Exception as e:
            error_msg = f"Error handling drop: {e}"
            self.result_label.config(text=error_msg, fg='red')
            print(f"❌ {error_msg}")
    
    def handle_drag_enter(self, event):
        """Handle drag enter events."""
        self.drop_area.config(bg='lightgreen')
        print("🔄 Drag entered")
    
    def handle_drag_leave(self, event):
        """Handle drag leave events."""
        self.drop_area.config(bg='lightblue')
        print("🔄 Drag left")

if __name__ == "__main__":
    print("\n" + "="*50)
    print("DRAG AND DROP TEST")
    print("="*50)
    
    if DRAG_DROP_AVAILABLE:
        print("✅ tkinterdnd2 is installed and available")
        print("✅ Drag and drop functionality should work")
    else:
        print("❌ tkinterdnd2 is NOT installed")
        print("❌ Drag and drop will not work")
        print("\nTo fix this issue:")
        print("1. Open a terminal/command prompt")
        print("2. Navigate to your project directory")
        print("3. Activate your virtual environment (if using one)")
        print("4. Run: pip install tkinterdnd2")
        print("5. Restart the application")
    
    print("\nStarting test application...")
    print("="*50 + "\n")
    
    app = DragDropTest()
    app.mainloop()