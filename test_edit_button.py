#!/usr/bin/env python3
"""
Test script to check the visibility and functionality of the Edit Image button.
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modern_gui import ModernFluxGUI
    print("✅ Successfully imported ModernFluxGUI")
except ImportError as e:
    print(f"❌ Failed to import ModernFluxGUI: {e}")
    sys.exit(1)

def test_edit_button_visibility():
    """Test if the edit button is visible and accessible."""
    print("\n🔍 Testing Edit Button Visibility...")
    
    try:
        # Create the GUI
        root = tk.Tk()
        root.withdraw()  # Hide the main window initially
        
        app = ModernFluxGUI(root)
        print("✅ GUI created successfully")
        
        # Check if edit button exists
        if hasattr(app, 'edit_btn'):
            print("✅ Edit button attribute exists")
            
            # Check button properties
            button = app.edit_btn
            print(f"📝 Button text: {button.cget('text')}")
            print(f"📝 Button state: {button.cget('state')}")
            print(f"📝 Button style: {button.cget('style')}")
            
            # Check if button is packed/visible
            try:
                info = button.pack_info()
                print(f"✅ Button is packed: {info}")
            except tk.TclError:
                try:
                    info = button.grid_info()
                    print(f"✅ Button is gridded: {info}")
                except tk.TclError:
                    print("❌ Button is not packed or gridded - might not be visible")
            
            # Check parent widget
            parent = button.master
            print(f"📝 Button parent: {parent}")
            
            # Try to get button's position and size
            try:
                root.deiconify()  # Show window to get geometry
                root.update_idletasks()
                
                # Switch to editing tab first
                if hasattr(app, 'notebook'):
                    app.notebook.select(1)  # Select editing tab (index 1)
                    root.update_idletasks()
                    print("✅ Switched to editing tab")
                
                x = button.winfo_x()
                y = button.winfo_y()
                width = button.winfo_width()
                height = button.winfo_height()
                print(f"📍 Button position: ({x}, {y})")
                print(f"📏 Button size: {width}x{height}")
                
                if width > 0 and height > 0:
                    print("✅ Button has visible dimensions")
                else:
                    print("❌ Button has zero dimensions - might be hidden")
                    
            except Exception as e:
                print(f"⚠️ Could not get button geometry: {e}")
            
        else:
            print("❌ Edit button attribute does not exist")
            
        # Check if editing tab exists
        if hasattr(app, 'notebook'):
            tabs = app.notebook.tabs()
            print(f"📝 Available tabs: {len(tabs)}")
            for i, tab in enumerate(tabs):
                tab_text = app.notebook.tab(tab, 'text')
                print(f"  Tab {i}: {tab_text}")
        
        # Keep window open for a few seconds to visually inspect
        print("\n👀 Window will be visible for 5 seconds for visual inspection...")
        root.deiconify()
        root.after(5000, root.quit)  # Close after 5 seconds
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Edit Button Visibility Test")
    print("=" * 40)
    test_edit_button_visibility()
    print("\n✅ Test completed")