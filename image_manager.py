"""
Image Management System for FLUX.1 Kontext Pro Image Generator
Handles image downloading, caching, preview, save/export functionality with proper file management.
"""

import asyncio
import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from urllib.parse import urlparse
import hashlib

import aiohttp
import aiohttp.client_exceptions
from PIL import Image, ImageTk
import tkinter as tk


@dataclass
class ImageInfo:
    """Information about a generated or edited image."""
    url: str
    local_path: Optional[str] = None
    filename: str = ""
    content_type: str = "image/jpeg"
    file_size: int = 0
    width: int = 0
    height: int = 0
    prompt: str = ""
    seed: Optional[int] = None
    timestamp: str = ""
    thumbnail_path: Optional[str] = None
    # Editing-specific fields
    is_edited: bool = False
    original_image_url: Optional[str] = None
    editing_prompt: Optional[str] = None
    parent_image_path: Optional[str] = None  # Path to the original image if this is an edit


class ImageCache:
    """Simple image cache for managing downloaded images."""
    
    def __init__(self, cache_dir: str = "cache", max_size_mb: int = 500):
        """
        Initialize the image cache.
        
        Args:
            cache_dir: Directory for cached images.
            max_size_mb: Maximum cache size in megabytes.
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.cache_index = {}  # url -> ImageInfo
        self.logger = logging.getLogger(__name__)
    
    def _get_cache_key(self, url: str) -> str:
        """Generate a cache key from URL."""
        return hashlib.md5(url.encode()).hexdigest()
    
    def get(self, url: str) -> Optional[ImageInfo]:
        """Get cached image info by URL."""
        return self.cache_index.get(url)
    
    def put(self, url: str, image_info: ImageInfo) -> None:
        """Add image info to cache."""
        self.cache_index[url] = image_info
        self._cleanup_if_needed()
    
    def _cleanup_if_needed(self) -> None:
        """Clean up cache if it exceeds size limit."""
        total_size = sum(
            Path(info.local_path).stat().st_size 
            for info in self.cache_index.values() 
            if info.local_path and Path(info.local_path).exists()
        )
        
        if total_size > self.max_size_bytes:
            # Remove oldest files first
            sorted_items = sorted(
                self.cache_index.items(),
                key=lambda x: Path(x[1].local_path).stat().st_mtime if x[1].local_path else 0
            )
            
            for url, info in sorted_items:
                if info.local_path and Path(info.local_path).exists():
                    try:
                        Path(info.local_path).unlink()
                        if info.thumbnail_path and Path(info.thumbnail_path).exists():
                            Path(info.thumbnail_path).unlink()
                        del self.cache_index[url]
                        self.logger.info(f"Removed cached image: {info.filename}")
                        
                        # Check if we're under the limit now
                        total_size -= Path(info.local_path).stat().st_size
                        if total_size <= self.max_size_bytes * 0.8:  # 80% threshold
                            break
                    except Exception as e:
                        self.logger.error(f"Error removing cached file: {e}")


class ImageManager:
    """
    Comprehensive image management system for the FLUX.1 Kontext Pro Image Generator.
    Handles downloading, caching, thumbnails, and file operations.
    """
    
    def __init__(self, output_dir: str = "output", cache_dir: str = "cache"):
        """
        Initialize the image manager.
        
        Args:
            output_dir: Directory for saved images.
            cache_dir: Directory for cached images.
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.cache = ImageCache(cache_dir)
        self.logger = logging.getLogger(__name__)
        
        # Create subdirectories
        (self.output_dir / "thumbnails").mkdir(exist_ok=True)
        (self.output_dir / "prompts").mkdir(exist_ok=True)
    
    async def download_image(self, url: str, prompt: str = "", seed: Optional[int] = None,
                           session: Optional[aiohttp.ClientSession] = None) -> ImageInfo:
        """
        Download an image from URL and create ImageInfo.
        
        Args:
            url: Image URL to download.
            prompt: The prompt used to generate the image.
            seed: The seed used for generation.
            session: Optional aiohttp session to use.
            
        Returns:
            ImageInfo object with download details.
        """
        # Check cache first
        cached = self.cache.get(url)
        if cached and cached.local_path and Path(cached.local_path).exists():
            self.logger.info(f"Using cached image: {cached.filename}")
            return cached
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        parsed_url = urlparse(url)
        extension = Path(parsed_url.path).suffix or ".jpg"
        
        seed_str = f"seed_{seed}" if seed is not None else "no_seed"
        filename = f"{timestamp}_{seed_str}{extension}"
        local_path = self.output_dir / filename
        
        # Download the image with retry logic
        return await self._download_image_with_retry(
            url, local_path, filename, prompt, seed, timestamp, session
        )
    
    async def download_images_batch(self, urls: List[str], prompt: str = "", 
                                  seed: Optional[int] = None) -> List[ImageInfo]:
        """
        Download multiple images concurrently.
        
        Args:
            urls: List of image URLs to download.
            prompt: The prompt used to generate the images.
            seed: The seed used for generation.
            
        Returns:
            List of ImageInfo objects.
        """
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.download_image(url, prompt, seed, session)
                for url in urls
            ]
            return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _create_thumbnail(self, image_info: ImageInfo, size: Tuple[int, int] = (200, 200)) -> None:
        """Create a thumbnail for the image."""
        if not image_info.local_path:
            return
        
        try:
            thumbnail_path = self.output_dir / "thumbnails" / f"thumb_{image_info.filename}"
            
            with Image.open(image_info.local_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, "JPEG", quality=85)
            
            image_info.thumbnail_path = str(thumbnail_path)
            self.logger.debug(f"Created thumbnail: {thumbnail_path}")
            
        except Exception as e:
            self.logger.error(f"Error creating thumbnail: {e}")
    
    async def _save_prompt(self, prompt: str, seed: Optional[int], timestamp: str) -> None:
        """Save the prompt to a text file."""
        try:
            seed_str = f"seed_{seed}" if seed is not None else "no_seed"
            prompt_filename = f"{timestamp}_{seed_str}_prompt.txt"
            prompt_path = self.output_dir / "prompts" / prompt_filename
            
            with open(prompt_path, 'w', encoding='utf-8') as f:
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Seed: {seed}\n")
                f.write(f"Prompt: {prompt}\n")
            
            self.logger.debug(f"Saved prompt: {prompt_filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving prompt: {e}")
    
    def get_thumbnail_for_display(self, image_info: ImageInfo, size: Tuple[int, int] = (150, 150)) -> Optional[ImageTk.PhotoImage]:
        """
        Get a PhotoImage suitable for tkinter display.
        
        Args:
            image_info: ImageInfo object.
            size: Desired thumbnail size.
            
        Returns:
            PhotoImage object or None if error.
        """
        try:
            if image_info.thumbnail_path and Path(image_info.thumbnail_path).exists():
                img_path = image_info.thumbnail_path
            elif image_info.local_path and Path(image_info.local_path).exists():
                img_path = image_info.local_path
            else:
                return None
            
            with Image.open(img_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                return ImageTk.PhotoImage(img)
        
        except Exception as e:
            self.logger.error(f"Error creating display thumbnail: {e}")
            return None
    
    def export_image(self, image_info: ImageInfo, destination: str) -> bool:
        """
        Export/copy an image to a destination path.
        
        Args:
            image_info: ImageInfo object.
            destination: Destination file path.
            
        Returns:
            True if successful, False otherwise.
        """
        try:
            if not image_info.local_path or not Path(image_info.local_path).exists():
                self.logger.error("Source image file not found")
                return False
            
            dest_path = Path(destination)
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(image_info.local_path, dest_path)
            self.logger.info(f"Exported image to: {destination}")
            return True
        
        except Exception as e:
            self.logger.error(f"Error exporting image: {e}")
            return False
    
    def get_recent_images(self, limit: int = 20) -> List[ImageInfo]:
        """
        Get recently downloaded images.
        
        Args:
            limit: Maximum number of images to return.
            
        Returns:
            List of ImageInfo objects sorted by timestamp (newest first).
        """
        images = list(self.cache.cache_index.values())
        images.sort(key=lambda x: x.timestamp, reverse=True)
        return images[:limit]
    
    def cleanup_old_files(self, days: int = 30) -> int:
        """
        Clean up files older than specified days.
        
        Args:
            days: Number of days to keep files.
            
        Returns:
            Number of files cleaned up.
        """
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        cleaned_count = 0
        
        for image_info in list(self.cache.cache_index.values()):
            if image_info.local_path:
                try:
                    file_path = Path(image_info.local_path)
                    if file_path.exists() and file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        if image_info.thumbnail_path:
                            Path(image_info.thumbnail_path).unlink(missing_ok=True)
                        
                        # Remove from cache
                        for url, info in list(self.cache.cache_index.items()):
                            if info == image_info:
                                del self.cache.cache_index[url]
                                break
                        
                        cleaned_count += 1
                        self.logger.info(f"Cleaned up old file: {image_info.filename}")
                
                except Exception as e:
                    self.logger.error(f"Error cleaning up file: {e}")
        
        return cleaned_count

    async def download_edited_image(self, url: str, editing_prompt: str = "",
                                   original_image_url: str = "", seed: Optional[int] = None,
                                   session: Optional[aiohttp.ClientSession] = None) -> ImageInfo:
        """
        Download an edited image from URL and create ImageInfo with editing metadata.

        Args:
            url: Edited image URL to download.
            editing_prompt: The prompt used for editing.
            original_image_url: URL of the original image that was edited.
            seed: The seed used for editing.
            session: Optional aiohttp session to use.

        Returns:
            ImageInfo object with editing details.
        """
        # Check cache first
        cached = self.cache.get(url)
        if cached and cached.local_path and Path(cached.local_path).exists():
            self.logger.info(f"Using cached edited image: {cached.filename}")
            return cached

        # Generate filename for edited image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        parsed_url = urlparse(url)
        extension = Path(parsed_url.path).suffix or ".png"

        seed_str = f"seed_{seed}" if seed is not None else "no_seed"
        filename = f"{timestamp}_edited_{seed_str}{extension}"
        local_path = self.output_dir / filename

        # Download the edited image with retry logic
        return await self._download_with_retry(
            url, local_path, filename, editing_prompt, original_image_url, seed, timestamp, session
        )

    async def _download_with_retry(self, url: str, local_path: Path, filename: str,
                                 editing_prompt: str, original_image_url: str,
                                 seed: Optional[int], timestamp: str,
                                 session: Optional[aiohttp.ClientSession] = None,
                                 max_retries: int = 3, retry_delay: float = 1.0) -> ImageInfo:
        """
        Download an image with retry logic and robust error handling.

        Args:
            url: Image URL to download
            local_path: Local path to save the image
            filename: Filename for the image
            editing_prompt: The editing prompt used
            original_image_url: URL of the original image
            seed: Seed used for editing
            timestamp: Timestamp for the download
            session: Optional aiohttp session
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            ImageInfo object with download details

        Raises:
            Exception: If download fails after all retries
        """
        close_session = False
        if session is None:
            # Create session with custom connector for better DNS handling
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=60, connect=30)
            )
            close_session = True

        last_exception = None

        try:
            for attempt in range(max_retries + 1):
                try:
                    self.logger.info(f"Downloading edited image (attempt {attempt + 1}/{max_retries + 1}): {url}")

                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.read()

                            # Save to file
                            with open(local_path, 'wb') as f:
                                f.write(content)

                            # Get image dimensions
                            try:
                                with Image.open(local_path) as img:
                                    width, height = img.size
                            except Exception as e:
                                self.logger.warning(f"Could not get image dimensions: {e}")
                                width = height = 0

                            # Create ImageInfo for edited image
                            image_info = ImageInfo(
                                url=url,
                                local_path=str(local_path),
                                filename=filename,
                                content_type=response.headers.get('content-type', 'image/png'),
                                file_size=len(content),
                                width=width,
                                height=height,
                                prompt=editing_prompt,
                                seed=seed,
                                timestamp=timestamp,
                                is_edited=True,
                                original_image_url=original_image_url,
                                editing_prompt=editing_prompt
                            )

                            # Create thumbnail
                            await self._create_thumbnail(image_info)

                            # Save editing prompt if provided
                            if editing_prompt:
                                await self._save_editing_prompt(editing_prompt, original_image_url, seed, timestamp)

                            # Cache the image info
                            self.cache.put(url, image_info)

                            self.logger.info(f"Successfully downloaded edited image: {filename}")
                            return image_info

                        else:
                            raise Exception(f"HTTP {response.status}: {response.reason}")

                except (aiohttp.ClientConnectorDNSError, aiohttp.ClientConnectorError) as e:
                    last_exception = e
                    self.logger.warning(f"Network error downloading edited image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

                except aiohttp.ClientTimeout as e:
                    last_exception = e
                    self.logger.warning(f"Timeout downloading edited image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

                except Exception as e:
                    last_exception = e
                    self.logger.error(f"Unexpected error downloading edited image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

        finally:
            if close_session and session:
                await session.close()

        # If we get here, all retries failed
        error_msg = f"Failed to download edited image after {max_retries + 1} attempts"
        if last_exception:
            if isinstance(last_exception, aiohttp.ClientConnectorDNSError):
                error_msg += f". DNS resolution failed for {url}. Please check your internet connection."
            elif isinstance(last_exception, aiohttp.ClientConnectorError):
                error_msg += f". Connection failed: {str(last_exception)}"
            elif isinstance(last_exception, aiohttp.ClientTimeout):
                error_msg += f". Request timed out: {str(last_exception)}"
            else:
                error_msg += f". Last error: {str(last_exception)}"

        self.logger.error(error_msg)
        raise Exception(error_msg)

    async def _download_image_with_retry(self, url: str, local_path: Path, filename: str,
                                       prompt: str, seed: Optional[int], timestamp: str,
                                       session: Optional[aiohttp.ClientSession] = None,
                                       max_retries: int = 3, retry_delay: float = 1.0) -> ImageInfo:
        """
        Download a regular image with retry logic and robust error handling.

        Args:
            url: Image URL to download
            local_path: Local path to save the image
            filename: Filename for the image
            prompt: The prompt used to generate the image
            seed: Seed used for generation
            timestamp: Timestamp for the download
            session: Optional aiohttp session
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            ImageInfo object with download details

        Raises:
            Exception: If download fails after all retries
        """
        close_session = False
        if session is None:
            # Create session with custom connector for better DNS handling
            connector = aiohttp.TCPConnector(
                limit=10,
                limit_per_host=5,
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=60, connect=30)
            )
            close_session = True

        last_exception = None

        try:
            for attempt in range(max_retries + 1):
                try:
                    self.logger.info(f"Downloading image (attempt {attempt + 1}/{max_retries + 1}): {url}")

                    async with session.get(url) as response:
                        if response.status == 200:
                            content = await response.read()

                            # Save to file
                            with open(local_path, 'wb') as f:
                                f.write(content)

                            # Get image dimensions
                            try:
                                with Image.open(local_path) as img:
                                    width, height = img.size
                            except Exception as e:
                                self.logger.warning(f"Could not get image dimensions: {e}")
                                width = height = 0

                            # Create ImageInfo
                            image_info = ImageInfo(
                                url=url,
                                local_path=str(local_path),
                                filename=filename,
                                content_type=response.headers.get('content-type', 'image/jpeg'),
                                file_size=len(content),
                                width=width,
                                height=height,
                                prompt=prompt,
                                seed=seed,
                                timestamp=timestamp
                            )

                            # Create thumbnail
                            await self._create_thumbnail(image_info)

                            # Save prompt if provided
                            if prompt:
                                await self._save_prompt(prompt, seed, timestamp)

                            # Cache the image info
                            self.cache.put(url, image_info)

                            self.logger.info(f"Successfully downloaded image: {filename}")
                            return image_info

                        else:
                            raise Exception(f"HTTP {response.status}: {response.reason}")

                except (aiohttp.ClientConnectorDNSError, aiohttp.ClientConnectorError) as e:
                    last_exception = e
                    self.logger.warning(f"Network error downloading image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

                except aiohttp.ClientTimeout as e:
                    last_exception = e
                    self.logger.warning(f"Timeout downloading image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

                except Exception as e:
                    last_exception = e
                    self.logger.error(f"Unexpected error downloading image (attempt {attempt + 1}): {str(e)}")

                    if attempt < max_retries:
                        wait_time = retry_delay * (2 ** attempt)
                        self.logger.info(f"Retrying in {wait_time:.1f} seconds...")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        break

        finally:
            if close_session and session:
                await session.close()

        # If we get here, all retries failed
        error_msg = f"Failed to download image after {max_retries + 1} attempts"
        if last_exception:
            if isinstance(last_exception, aiohttp.ClientConnectorDNSError):
                error_msg += f". DNS resolution failed for {url}. Please check your internet connection."
            elif isinstance(last_exception, aiohttp.ClientConnectorError):
                error_msg += f". Connection failed: {str(last_exception)}"
            elif isinstance(last_exception, aiohttp.ClientTimeout):
                error_msg += f". Request timed out: {str(last_exception)}"
            else:
                error_msg += f". Last error: {str(last_exception)}"

        self.logger.error(error_msg)
        raise Exception(error_msg)

    async def download_edited_images_batch(self, urls: List[str], editing_prompt: str = "",
                                         original_image_url: str = "", seed: Optional[int] = None) -> List[ImageInfo]:
        """
        Download multiple edited images concurrently.

        Args:
            urls: List of edited image URLs to download.
            editing_prompt: The prompt used for editing.
            original_image_url: URL of the original image that was edited.
            seed: The seed used for editing.

        Returns:
            List of ImageInfo objects.
        """
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.download_edited_image(url, editing_prompt, original_image_url, seed, session)
                for url in urls
            ]
            return await asyncio.gather(*tasks, return_exceptions=True)

    async def _save_editing_prompt(self, editing_prompt: str, original_image_url: str,
                                  seed: Optional[int], timestamp: str) -> None:
        """Save the editing prompt and metadata to a text file."""
        try:
            seed_str = f"seed_{seed}" if seed is not None else "no_seed"
            prompt_filename = f"{timestamp}_edited_{seed_str}_prompt.txt"
            prompt_path = self.output_dir / "prompts" / prompt_filename

            with open(prompt_path, 'w', encoding='utf-8') as f:
                f.write(f"Timestamp: {timestamp}\n")
                f.write(f"Type: Image Editing\n")
                f.write(f"Seed: {seed}\n")
                f.write(f"Original Image URL: {original_image_url}\n")
                f.write(f"Editing Prompt: {editing_prompt}\n")

            self.logger.debug(f"Saved editing prompt: {prompt_filename}")

        except Exception as e:
            self.logger.error(f"Error saving editing prompt: {e}")

    def get_image_for_editing(self, image_info: ImageInfo) -> Optional[str]:
        """
        Get the local path of an image for editing purposes.

        Args:
            image_info: ImageInfo object.

        Returns:
            Local file path if available, None otherwise.
        """
        if image_info.local_path and Path(image_info.local_path).exists():
            return image_info.local_path
        return None

    def create_editing_history_entry(self, original_image: ImageInfo, edited_image: ImageInfo) -> None:
        """
        Create a link between original and edited images for history tracking.

        Args:
            original_image: The original ImageInfo object.
            edited_image: The edited ImageInfo object.
        """
        if original_image.local_path:
            edited_image.parent_image_path = original_image.local_path
            self.logger.info(f"Linked edited image {edited_image.filename} to original {original_image.filename}")

    def validate_image_file(self, file_path: str) -> bool:
        """
        Validate that a file is a supported image format.

        Args:
            file_path: Path to the image file.

        Returns:
            True if valid image file, False otherwise.
        """
        try:
            file_path = Path(file_path)

            # Check if file exists
            if not file_path.exists():
                self.logger.error(f"File does not exist: {file_path}")
                return False

            # Check file extension
            valid_extensions = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.tif'}
            if file_path.suffix.lower() not in valid_extensions:
                self.logger.error(f"Unsupported file extension: {file_path.suffix}")
                return False

            # Try to open with PIL to validate it's a real image
            with Image.open(file_path) as img:
                # Verify it's a valid image by accessing basic properties
                width, height = img.size
                if width <= 0 or height <= 0:
                    self.logger.error(f"Invalid image dimensions: {width}x{height}")
                    return False

            self.logger.info(f"Image file validation passed: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Image validation failed for {file_path}: {e}")
            return False

    def create_uploaded_image_info(self, file_path: str, uploaded_url: str) -> ImageInfo:
        """
        Create an ImageInfo object for an uploaded image.

        Args:
            file_path: Local path to the uploaded image file.
            uploaded_url: URL of the uploaded image from FAL storage.

        Returns:
            ImageInfo object for the uploaded image.
        """
        try:
            file_path = Path(file_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Get image information
            with Image.open(file_path) as img:
                width, height = img.size
                format_name = img.format.lower() if img.format else 'unknown'

            # Get file size
            file_size = file_path.stat().st_size

            # Create filename for local copy
            filename = f"{timestamp}_uploaded_{file_path.name}"
            local_copy_path = self.output_dir / filename

            # Copy to output directory
            import shutil
            shutil.copy2(file_path, local_copy_path)

            # Create ImageInfo
            image_info = ImageInfo(
                url=uploaded_url,
                local_path=str(local_copy_path),
                filename=filename,
                content_type=f"image/{format_name}",
                file_size=file_size,
                width=width,
                height=height,
                prompt="Uploaded image",
                timestamp=timestamp,
                is_edited=False
            )

            # Create thumbnail
            asyncio.create_task(self._create_thumbnail(image_info))

            # Cache the image info
            self.cache.put(uploaded_url, image_info)

            self.logger.info(f"Created ImageInfo for uploaded image: {filename}")
            return image_info

        except Exception as e:
            self.logger.error(f"Failed to create ImageInfo for uploaded image: {e}")
            raise Exception(f"Failed to process uploaded image: {e}")

    def get_supported_image_formats(self) -> List[Tuple[str, str]]:
        """
        Get list of supported image formats for file dialogs.

        Returns:
            List of tuples (description, pattern) for file dialog filters.
        """
        return [
            ("Image files", "*.jpg *.jpeg *.png *.webp *.bmp *.tiff *.tif"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("WebP files", "*.webp"),
            ("BMP files", "*.bmp"),
            ("TIFF files", "*.tiff *.tif"),
            ("All files", "*.*")
        ]
