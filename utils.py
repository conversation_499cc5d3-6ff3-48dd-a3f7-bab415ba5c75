import logging

def get_user_input(prompt, default=None):
    """Helper function to get user input with a default value."""
    user_input = input(f"{prompt} (default: {default}): ")
    return user_input if user_input else default

def save_prompt(prompt, config, seed, timestamp):
    """Save the prompt to a text file with a unique prefix."""
    output_folder = config["output_directory"]
    filename = f"{output_folder}/{timestamp}_seed_{seed}_prompt.txt"
    with open(filename, "w") as f:
        f.write(prompt)
    logging.info(f"Prompt saved as {filename}")
    print(f"Prompt saved as {filename}")
