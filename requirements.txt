# Core API and HTTP client
fal-client>=0.4.0
aiohttp>=3.8.0

# Image processing and GUI
Pillow>=10.0.0

# Data handling and validation
dataclasses-json>=0.6.0

# Async and threading support
asyncio>=3.4.3

# Enhanced GUI components for drag and drop
tkinterdnd2>=0.3.0

# Optional: Additional GUI components (uncomment if needed)
# tkinter-tooltip>=2.0.0
# customtkinter>=5.0.0

# Note: tkinter is included with Python standard library
# Note: threading, concurrent.futures, pathlib, json, os, sys, logging are standard library modules