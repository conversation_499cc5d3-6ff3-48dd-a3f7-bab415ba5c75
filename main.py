"""
Main Application Entry Point for FLUX.1 Kontext Pro Image Generator
Handles application initialization, lifecycle management, and error handling.
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Optional

# Add the current directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent))

from logger import setup_logging, get_logger, get_error_handler, ErrorType, handle_exception
from modern_gui import ModernFluxGUI
from settings import ConfigManager


class FluxApplication:
    """
    Main application class that manages the FLUX.1 Kontext Pro Image Generator.
    Handles initialization, configuration, and lifecycle management.
    """

    def __init__(self, config_path: Optional[str] = None, debug: bool = False):
        """
        Initialize the FLUX application.

        Args:
            config_path: Optional path to configuration file.
            debug: Enable debug mode.
        """
        self.config_path = config_path
        self.debug = debug
        self.gui = None
        self.logger = None
        self.config_manager = None

        # Setup application
        self._setup_application()

    def _setup_application(self):
        """Setup the application components."""
        try:
            # Setup logging first
            self.logger = setup_logging(
                log_file="flux_generator.log",
                console_output=self.debug,
                gui_callback=None  # Will be set after GUI is created
            )

            self.logger.info("Starting FLUX.1 Kontext Pro Image Generator")

            # Initialize configuration manager
            self.config_manager = ConfigManager(self.config_path)
            self.logger.info("Configuration loaded successfully")

            # Ensure output directory exists
            self.config_manager.ensure_output_directory()

            self.logger.info("Application setup completed successfully")

        except Exception as e:
            handle_exception(
                e,
                ErrorType.CONFIG_ERROR,
                "Application setup",
                show_dialog=False
            )
            sys.exit(1)

    def run(self):
        """Run the main application."""
        try:
            self.logger.info("Starting GUI application")

            # Create and run the GUI
            self.gui = ModernFluxGUI()

            # Connect logger to GUI
            if hasattr(self.gui, 'log_message'):
                self.logger.gui_callback = self.gui.log_message

            self.logger.info("GUI initialized successfully")

            # Start the main loop
            self.gui.mainloop()

        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
            self._cleanup()
        except Exception as e:
            handle_exception(
                e,
                ErrorType.GUI_ERROR,
                "GUI execution",
                show_dialog=True
            )
        finally:
            self._cleanup()

    def _cleanup(self):
        """Cleanup application resources."""
        try:
            if self.logger:
                self.logger.info("Shutting down FLUX.1 Kontext Pro Image Generator")

            # Cleanup GUI resources
            if self.gui:
                try:
                    self.gui.quit()
                except:
                    pass

            # Save configuration if needed
            if self.config_manager:
                config = self.config_manager.get_config()
                if config.auto_save_settings:
                    self.config_manager.save_config()

            if self.logger:
                self.logger.info("Application shutdown completed")

        except Exception as e:
            print(f"Error during cleanup: {e}")


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="FLUX.1 Kontext Pro Image Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with default settings
  python main.py --debug            # Run with debug logging
  python main.py --config my.json   # Use custom config file
        """
    )

    parser.add_argument(
        '--config', '-c',
        type=str,
        help='Path to configuration file'
    )

    parser.add_argument(
        '--debug', '-d',
        action='store_true',
        help='Enable debug mode with console logging'
    )

    parser.add_argument(
        '--version', '-v',
        action='version',
        version='FLUX.1 Kontext Pro Image Generator v1.0'
    )

    return parser.parse_args()


def check_dependencies():
    """Check if all required dependencies are available."""
    required_modules = [
        'tkinter',
        'PIL',
        'aiohttp',
        'fal_client'
    ]

    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print("Error: Missing required dependencies:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\nPlease install missing dependencies using:")
        print("  pip install -r requirements.txt")
        return False

    return True


def main():
    """Main entry point."""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Check dependencies
        if not check_dependencies():
            sys.exit(1)

        # Create and run application
        app = FluxApplication(
            config_path=args.config,
            debug=args.debug
        )

        app.run()

    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
