import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import settings
import generator
import threading
import os
import subprocess
import sys
from datetime import datetime


class ModernImageGeneratorGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("🎨 FAL AI Image Generator Pro")
        self.geometry("1000x700")
        self.minsize(800, 600)
        
        # Configure modern colors
        self.colors = {
            'bg': '#f0f0f0',
            'card': '#ffffff',
            'primary': '#2563eb',
            'primary_hover': '#1d4ed8',
            'secondary': '#64748b',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'text': '#1f2937',
            'text_light': '#6b7280',
            'border': '#e5e7eb'
        }
        
        self.configure(bg=self.colors['bg'])
        
        # Setup styles
        self.setup_styles()
        
        self.config = settings.load_config()
        settings.ensure_output_directory(self.config)
        self.default_settings = self.config["default_settings"]
        
        self.create_widgets()
        
        # Center window on screen
        self.center_window()

    def setup_styles(self):
        """Configure modern ttk styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure button styles
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 10, 'bold'))
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_hover'])])
        
        style.configure('Secondary.TButton',
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 9))
        style.map('Secondary.TButton',
                 background=[('active', '#475569')])
        
        style.configure('Success.TButton',
                       background=self.colors['success'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       font=('Segoe UI', 9))
        
        # Configure frame styles
        style.configure('Card.TFrame',
                       background=self.colors['card'],
                       relief='flat',
                       borderwidth=1)
        
        # Configure label styles
        style.configure('Title.TLabel',
                       background=self.colors['card'],
                       foreground=self.colors['text'],
                       font=('Segoe UI', 16, 'bold'))
        
        style.configure('Subtitle.TLabel',
                       background=self.colors['card'],
                       foreground=self.colors['text_light'],
                       font=('Segoe UI', 11))
        
        style.configure('Card.TLabel',
                       background=self.colors['card'],
                       foreground=self.colors['text'],
                       font=('Segoe UI', 9))

    def center_window(self):
        """Center the window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def create_widgets(self):
        # Main container with padding
        main_container = tk.Frame(self, bg=self.colors['bg'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header section
        self.create_header(main_container)
        
        # Content area with two columns
        content_frame = tk.Frame(main_container, bg=self.colors['bg'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # Left column - Input and controls
        left_frame = ttk.Frame(content_frame, style='Card.TFrame', padding=20)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Right column - Output and logs
        right_frame = ttk.Frame(content_frame, style='Card.TFrame', padding=20)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        self.create_input_section(left_frame)
        self.create_output_section(right_frame)

    def create_header(self, parent):
        """Create the header section"""
        header_frame = ttk.Frame(parent, style='Card.TFrame', padding=20)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title and description
        title_label = ttk.Label(header_frame, text="🎨 AI Image Generator Pro", style='Title.TLabel')
        title_label.pack(anchor=tk.W)
        
        subtitle_label = ttk.Label(header_frame, 
                                  text="Create stunning images with advanced AI models", 
                                  style='Subtitle.TLabel')
        subtitle_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Status bar
        status_frame = tk.Frame(header_frame, bg=self.colors['card'])
        status_frame.pack(fill=tk.X, pady=(15, 0))
        
        self.status_label = ttk.Label(status_frame, 
                                     text=f"Ready • Model: {self.default_settings.get('model', 'N/A')}", 
                                     style='Card.TLabel')
        self.status_label.pack(side=tk.LEFT)

    def create_input_section(self, parent):
        """Create the input and controls section"""
        # Prompt section
        prompt_label = ttk.Label(parent, text="Prompt", style='Card.TLabel', font=('Segoe UI', 11, 'bold'))
        prompt_label.pack(anchor=tk.W, pady=(0, 5))
        
        # Prompt text area with scrollbar
        prompt_frame = tk.Frame(parent, bg=self.colors['card'])
        prompt_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.prompt_text = tk.Text(prompt_frame, height=4, wrap=tk.WORD, 
                                  font=('Segoe UI', 10), relief=tk.FLAT, 
                                  borderwidth=1, highlightthickness=1,
                                  highlightcolor=self.colors['primary'])
        self.prompt_text.pack(fill=tk.X)
        
        # Quick prompts
        quick_frame = tk.Frame(parent, bg=self.colors['card'])
        quick_frame.pack(fill=tk.X, pady=(0, 20))
        
        ttk.Label(quick_frame, text="Quick Prompts:", style='Card.TLabel', font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)
        
        quick_prompts = [
            "A majestic landscape with mountains and lakes",
            "Futuristic city at sunset",
            "Abstract art with vibrant colors",
            "Portrait of a wise old wizard"
        ]
        
        for i, prompt in enumerate(quick_prompts):
            btn = tk.Button(quick_frame, text=prompt, 
                           command=lambda p=prompt: self.set_prompt(p),
                           bg=self.colors['border'], fg=self.colors['text'],
                           relief=tk.FLAT, font=('Segoe UI', 8),
                           cursor='hand2')
            btn.pack(fill=tk.X, pady=1)

        # Controls section
        controls_label = ttk.Label(parent, text="Controls", style='Card.TLabel', font=('Segoe UI', 11, 'bold'))
        controls_label.pack(anchor=tk.W, pady=(20, 10))
        
        # Buttons grid
        btn_frame = tk.Frame(parent, bg=self.colors['card'])
        btn_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.generate_button = ttk.Button(btn_frame, text="🚀 Generate Image", 
                                         command=self.generate_image, style='Primary.TButton')
        self.generate_button.pack(fill=tk.X, pady=(0, 10))
        
        btn_row1 = tk.Frame(btn_frame, bg=self.colors['card'])
        btn_row1.pack(fill=tk.X, pady=(0, 5))
        
        self.settings_button = ttk.Button(btn_row1, text="⚙️ Settings", 
                                         command=self.open_settings, style='Secondary.TButton')
        self.settings_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        self.output_button = ttk.Button(btn_row1, text="📁 Output", 
                                       command=self.open_output_folder, style='Secondary.TButton')
        self.output_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        
        # Progress bar
        self.progress = ttk.Progressbar(parent, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(10, 0))

    def create_output_section(self, parent):
        """Create the output and logs section"""
        output_label = ttk.Label(parent, text="Generation Log", style='Card.TLabel', font=('Segoe UI', 11, 'bold'))
        output_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Log area with better styling
        log_frame = tk.Frame(parent, bg=self.colors['card'])
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, 
                                                 font=('Consolas', 9),
                                                 bg='#f8f9fa', fg=self.colors['text'],
                                                 relief=tk.FLAT, borderwidth=1,
                                                 highlightthickness=1,
                                                 highlightcolor=self.colors['border'])
        self.log_area.pack(fill=tk.BOTH, expand=True)
        
        # Log controls
        log_controls = tk.Frame(parent, bg=self.colors['card'])
        log_controls.pack(fill=tk.X, pady=(10, 0))
        
        clear_btn = ttk.Button(log_controls, text="🗑️ Clear Log", 
                              command=self.clear_log, style='Secondary.TButton')
        clear_btn.pack(side=tk.LEFT)
        
        save_log_btn = ttk.Button(log_controls, text="💾 Save Log", 
                                 command=self.save_log, style='Secondary.TButton')
        save_log_btn.pack(side=tk.RIGHT)

    def set_prompt(self, prompt):
        """Set a quick prompt"""
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, prompt)

    def clear_log(self):
        """Clear the log area"""
        self.log_area.delete(1.0, tk.END)

    def save_log(self):
        """Save log to file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Save log file"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_area.get(1.0, tk.END))
                messagebox.showinfo("Success", "Log saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Could not save log: {e}")

    def generate_image(self):
        prompt = self.prompt_text.get(1.0, tk.END).strip()
        if not prompt:
            messagebox.showwarning("Warning", "Please enter a prompt for image generation.")
            return

        self.log_area.insert(tk.END, f"\n{'='*50}\n")
        self.log_area.insert(tk.END, f"🚀 Starting generation at {datetime.now().strftime('%H:%M:%S')}\n")
        self.log_area.insert(tk.END, f"Prompt: {prompt}\n")
        self.log_area.insert(tk.END, f"{'='*50}\n\n")
        
        self.generate_button.config(state=tk.DISABLED, text="Generating...")
        self.progress.start(10)
        self.status_label.config(text="Generating image...")

        threading.Thread(target=self.run_generation_thread, args=(prompt,), daemon=True).start()

    def run_generation_thread(self, prompt):
        old_stdout = sys.stdout
        sys.stdout = self
        try:
            generator.run_asyncio_task(prompt, self.config, self.default_settings)
        finally:
            sys.stdout = old_stdout
            self.after(0, self.generation_complete)

    def generation_complete(self):
        """Called when generation is complete"""
        self.generate_button.config(state=tk.NORMAL, text="🚀 Generate Image")
        self.progress.stop()
        self.status_label.config(text=f"Ready • Model: {self.default_settings.get('model', 'N/A')}")
        self.log_area.insert(tk.END, "\n✅ Generation completed!\n\n")
        self.log_area.see(tk.END)

    def write(self, text):
        """Redirect stdout to log area"""
        self.log_area.insert(tk.END, text)
        self.log_area.see(tk.END)
        self.update_idletasks()

    def flush(self):
        pass

    def open_settings(self):
        ModernSettingsWindow(self, self.default_settings, self.save_settings)

    def save_settings(self, new_settings):
        self.default_settings = new_settings
        self.config["default_settings"] = self.default_settings
        settings.save_config(self.config)
        self.status_label.config(text=f"Ready • Model: {self.default_settings.get('model', 'N/A')}")
        messagebox.showinfo("Success", "Settings saved successfully!")

    def open_output_folder(self):
        output_dir = self.config.get("output_directory", "output")
        try:
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", output_dir])
            else:
                subprocess.run(["xdg-open", output_dir])
        except Exception as e:
            messagebox.showerror("Error", f"Could not open output folder: {e}")


class ModernSettingsWindow(tk.Toplevel):
    def __init__(self, parent, current_settings, save_callback):
        super().__init__(parent)
        self.title("⚙️ Settings - AI Image Generator")
        self.geometry("700x650")
        self.minsize(600, 500)
        self.transient(parent)
        self.grab_set()
        
        self.colors = parent.colors
        self.configure(bg=self.colors['bg'])
        
        self.current_settings = current_settings.copy()
        self.save_callback = save_callback
        
        # Add some modern colors for the settings
        self.settings_colors = {
            **self.colors,
            'accent': '#3b82f6',
            'accent_light': '#dbeafe', 
            'success_light': '#d1fae5',
            'warning_light': '#fef3c7',
            'section_bg': '#f8fafc',
            'hover': '#f1f5f9'
        }
        
        self.create_modern_widgets()
        self.center_window()

    def center_window(self):
        """Center the settings window on the parent"""
        self.update_idletasks()
        parent_x = self.master.winfo_x()
        parent_y = self.master.winfo_y()
        parent_w = self.master.winfo_width()
        parent_h = self.master.winfo_height()
        
        x = parent_x + (parent_w - self.winfo_width()) // 2
        y = parent_y + (parent_h - self.winfo_height()) // 2
        self.geometry(f"+{x}+{y}")

    def create_modern_widgets(self):
        # Main container
        main_container = tk.Frame(self, bg=self.colors['bg'])
        main_container.pack(fill=tk.BOTH, expand=True)
        
        # Header with gradient-like effect
        header = self.create_header(main_container)
        header.pack(fill=tk.X, pady=(0, 20))
        
        # Scrollable content area
        self.create_scrollable_content(main_container)
        
        # Footer with buttons
        footer = self.create_footer(main_container)
        footer.pack(fill=tk.X, side=tk.BOTTOM, pady=20)

    def create_header(self, parent):
        """Create a beautiful header section"""
        header_frame = tk.Frame(parent, bg=self.settings_colors['accent'], height=120)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Header content
        header_content = tk.Frame(header_frame, bg=self.settings_colors['accent'])
        header_content.pack(expand=True, fill=tk.BOTH, padx=30, pady=20)
        
        # Title
        title = tk.Label(header_content, text="⚙️ Generator Settings", 
                        bg=self.settings_colors['accent'], fg='white',
                        font=('Segoe UI', 18, 'bold'))
        title.pack(anchor=tk.W)
        
        # Subtitle
        subtitle = tk.Label(header_content, text="Customize your AI image generation experience", 
                           bg=self.settings_colors['accent'], fg='white',
                           font=('Segoe UI', 11), wraplength=400)
        subtitle.pack(anchor=tk.W, pady=(5, 0))
        
        # Current model indicator
        current_model = self.current_settings.get("model", "fal-ai/flux/schnell")
        model_display = "FLUX Schnell" if "schnell" in current_model else "FLUX Pro Kontext"
        status = tk.Label(header_content, text=f"Current Model: {model_display}", 
                         bg=self.settings_colors['accent'], fg=self.settings_colors['accent_light'],
                         font=('Segoe UI', 9))
        status.pack(anchor=tk.W, pady=(10, 0))
        
        return header_frame

    def create_scrollable_content(self, parent):
        """Create scrollable content area"""
        # Canvas and scrollbar setup
        canvas_frame = tk.Frame(parent, bg=self.colors['bg'])
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=20)
        
        self.canvas = tk.Canvas(canvas_frame, bg=self.colors['bg'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=self.colors['bg'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas
        self.bind_mousewheel(self.canvas)
        
        # Create content sections
        self.create_content_sections()

    def bind_mousewheel(self, widget):
        """Bind mousewheel scrolling to widget"""
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
        
        def _bind_to_mousewheel(event):
            self.canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        def _unbind_from_mousewheel(event):
            self.canvas.unbind_all("<MouseWheel>")
        
        widget.bind('<Enter>', _bind_to_mousewheel)
        widget.bind('<Leave>', _unbind_from_mousewheel)

    def create_content_sections(self):
        """Create all content sections"""
        # Model Selection Card
        self.create_model_selection_card()
        
        # Image Settings Card  
        self.create_image_settings_card()
        
        # Quality Settings Card
        self.create_quality_settings_card()
        
        # Advanced Settings Card
        self.create_advanced_settings_card()

    def create_card(self, title, emoji, description=None):
        """Create a modern card container"""
        card = tk.Frame(self.scrollable_frame, bg=self.colors['card'], relief='flat', bd=1)
        card.pack(fill=tk.X, pady=(0, 20), padx=10)
        
        # Add subtle shadow effect with border
        shadow = tk.Frame(card, bg=self.colors['border'], height=1)
        shadow.pack(fill=tk.X, side=tk.BOTTOM)
        
        # Card header
        header = tk.Frame(card, bg=self.settings_colors['section_bg'], height=50)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        header_content = tk.Frame(header, bg=self.settings_colors['section_bg'])
        header_content.pack(expand=True, fill=tk.BOTH, padx=20, pady=10)
        
        title_label = tk.Label(header_content, text=f"{emoji} {title}", 
                              bg=self.settings_colors['section_bg'], fg=self.colors['text'],
                              font=('Segoe UI', 12, 'bold'))
        title_label.pack(anchor=tk.W)
        
        if description:
            desc_label = tk.Label(header_content, text=description, 
                                 bg=self.settings_colors['section_bg'], fg=self.colors['text_light'],
                                 font=('Segoe UI', 9))
            desc_label.pack(anchor=tk.W)
        
        # Card content area
        content = tk.Frame(card, bg=self.colors['card'])
        content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        return content

    def create_model_selection_card(self):
        """Create model selection card"""
        content = self.create_card("AI Model Selection", "🤖", 
                                  "Choose the AI model that best fits your needs")
        
        # Get current model with fallback
        current_model = self.current_settings.get("model", "fal-ai/flux/schnell")
        self.model_var = tk.StringVar(value=current_model)
        
        # Model options
        models = [
            {
                "value": "fal-ai/flux/schnell",
                "title": "FLUX Schnell",
                "description": "Fast generation with good quality",
                "features": ["⚡ Quick results", "💰 Cost effective", "✨ Good quality"],
                "color": self.settings_colors['success']
            },
            {
                "value": "fal-ai/flux-pro/kontext/text-to-image", 
                "title": "FLUX Pro Kontext",
                "description": "Premium quality with advanced features",
                "features": ["🎯 Superior quality", "🔥 Advanced AI", "📈 Best results"],
                "color": self.settings_colors['accent']
            }
        ]
        
        for i, model in enumerate(models):
            self.create_model_option(content, model, i == 0)

    def create_model_option(self, parent, model_info, is_first):
        """Create a beautiful model option"""
        # Model container with hover effect
        model_frame = tk.Frame(parent, bg=self.colors['card'])
        if not is_first:
            model_frame.pack(fill=tk.X, pady=(15, 0))
        else:
            model_frame.pack(fill=tk.X)
        
        # Option container
        option_frame = tk.Frame(model_frame, bg=self.settings_colors['hover'], relief='flat', bd=1)
        option_frame.pack(fill=tk.X, pady=2)
        
        # Radio button and content
        radio_frame = tk.Frame(option_frame, bg=self.settings_colors['hover'])
        radio_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # Radio button
        radio = tk.Radiobutton(radio_frame, text="", variable=self.model_var, 
                              value=model_info["value"], bg=self.settings_colors['hover'],
                              activebackground=self.settings_colors['hover'],
                              selectcolor=model_info["color"], font=('Segoe UI', 10))
        radio.pack(side=tk.LEFT, anchor=tk.N, pady=2)
        
        # Content area
        content_area = tk.Frame(radio_frame, bg=self.settings_colors['hover'])
        content_area.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        
        # Title
        title = tk.Label(content_area, text=model_info["title"], 
                        bg=self.settings_colors['hover'], fg=self.colors['text'],
                        font=('Segoe UI', 11, 'bold'))
        title.pack(anchor=tk.W)
        
        # Description
        desc = tk.Label(content_area, text=model_info["description"], 
                       bg=self.settings_colors['hover'], fg=self.colors['text_light'],
                       font=('Segoe UI', 9))
        desc.pack(anchor=tk.W, pady=(2, 8))
        
        # Features
        features_frame = tk.Frame(content_area, bg=self.settings_colors['hover'])
        features_frame.pack(anchor=tk.W)
        
        for feature in model_info["features"]:
            feature_label = tk.Label(features_frame, text=feature, 
                                   bg=self.settings_colors['hover'], fg=self.colors['text_light'],
                                   font=('Segoe UI', 8))
            feature_label.pack(side=tk.LEFT, padx=(0, 15))

    def create_image_settings_card(self):
        """Create image settings card"""
        content = self.create_card("Image Properties", "🖼️", 
                                  "Configure the output image characteristics")
        
        # Image Size
        self.create_setting_row(content, "Image Size", "📐")
        size_frame = tk.Frame(content, bg=self.colors['card'])
        size_frame.pack(fill=tk.X, pady=(5, 15))
        
        self.image_size_var = tk.StringVar(value=self.current_settings.get("image_size", "landscape_4_3"))
        size_combo = ttk.Combobox(size_frame, textvariable=self.image_size_var, 
                                 values=['square_hd', 'square', 'portrait_4_3', 'portrait_16_9', 'landscape_4_3', 'landscape_16_9'],
                                 state='readonly', font=('Segoe UI', 10), width=25)
        size_combo.pack(anchor=tk.W)
        
        # Number of Images
        self.create_setting_row(content, "Number of Images", "🔢")
        images_frame = tk.Frame(content, bg=self.colors['card'])
        images_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.num_images_var = tk.IntVar(value=self.current_settings.get("num_images", 1))
        
        # Slider with modern styling
        slider_container = tk.Frame(images_frame, bg=self.colors['card'])
        slider_container.pack(fill=tk.X)
        
        scale = ttk.Scale(slider_container, from_=1, to=10, variable=self.num_images_var, 
                         orient=tk.HORIZONTAL, length=300)
        scale.pack(side=tk.LEFT)
        
        value_label = tk.Label(slider_container, textvariable=self.num_images_var, 
                              bg=self.colors['card'], fg=self.colors['text'],
                              font=('Segoe UI', 10, 'bold'), width=3)
        value_label.pack(side=tk.LEFT, padx=(15, 0))

    def create_quality_settings_card(self):
        """Create quality settings card"""
        content = self.create_card("Quality & Performance", "⚡", 
                                  "Fine-tune generation quality and speed")
        
        # Inference Steps
        self.create_setting_row(content, "Inference Steps", "🎯")
        steps_desc = tk.Label(content, text="Higher values = better quality but slower generation", 
                             bg=self.colors['card'], fg=self.colors['text_light'],
                             font=('Segoe UI', 8))
        steps_desc.pack(anchor=tk.W, pady=(0, 5))
        
        steps_frame = tk.Frame(content, bg=self.colors['card'])
        steps_frame.pack(fill=tk.X, pady=(5, 15))
        
        self.inf_steps_var = tk.IntVar(value=self.current_settings.get("num_inference_steps", 4))
        
        steps_scale = ttk.Scale(steps_frame, from_=1, to=50, variable=self.inf_steps_var, 
                               orient=tk.HORIZONTAL, length=300)
        steps_scale.pack(side=tk.LEFT)
        
        steps_value = tk.Label(steps_frame, textvariable=self.inf_steps_var, 
                              bg=self.colors['card'], fg=self.colors['text'],
                              font=('Segoe UI', 10, 'bold'), width=3)
        steps_value.pack(side=tk.LEFT, padx=(15, 0))
        
        # Safety Checker
        self.create_setting_row(content, "Safety Features", "🛡️")
        safety_frame = tk.Frame(content, bg=self.colors['card'])
        safety_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.safety_checker_var = tk.BooleanVar(value=self.current_settings.get("enable_safety_checker", False))
        
        safety_check = tk.Checkbutton(safety_frame, text="Enable content safety checker", 
                                     variable=self.safety_checker_var, bg=self.colors['card'],
                                     fg=self.colors['text'], font=('Segoe UI', 10),
                                     activebackground=self.colors['card'], 
                                     selectcolor=self.settings_colors['success'])
        safety_check.pack(anchor=tk.W)

    def create_advanced_settings_card(self):
        """Create advanced settings card"""
        content = self.create_card("Advanced Options", "🔧", 
                                  "Expert settings for reproducible results")
        
        # Seed Setting
        self.create_setting_row(content, "Random Seed", "🎲")
        seed_desc = tk.Label(content, text="Use the same seed to reproduce identical images", 
                            bg=self.colors['card'], fg=self.colors['text_light'],
                            font=('Segoe UI', 8))
        seed_desc.pack(anchor=tk.W, pady=(0, 5))
        
        seed_frame = tk.Frame(content, bg=self.colors['card'])
        seed_frame.pack(fill=tk.X, pady=(5, 0))
        
        # Seed input with modern styling
        seed_value = self.current_settings.get("seed")
        self.seed_var = tk.StringVar(value=str(seed_value) if seed_value else "")
        
        seed_input_frame = tk.Frame(seed_frame, bg=self.colors['card'])
        seed_input_frame.pack(anchor=tk.W)
        
        seed_entry = ttk.Entry(seed_input_frame, textvariable=self.seed_var, 
                              font=('Segoe UI', 10), width=20)
        seed_entry.pack(side=tk.LEFT)
        
        # Random seed button with modern styling
        random_btn = tk.Button(seed_input_frame, text="🎲 Random", 
                              command=self.generate_random_seed,
                              bg=self.settings_colors['accent'], fg='white',
                              font=('Segoe UI', 9), relief='flat', bd=0,
                              padx=15, pady=5, cursor='hand2')
        random_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # Add hover effect
        def on_enter(e):
            random_btn.configure(bg=self.settings_colors['accent'])
        def on_leave(e):
            random_btn.configure(bg=self.settings_colors['accent'])
        
        random_btn.bind("<Enter>", on_enter)
        random_btn.bind("<Leave>", on_leave)

    def create_setting_row(self, parent, title, emoji):
        """Create a setting row with emoji and title"""
        row = tk.Frame(parent, bg=self.colors['card'])
        row.pack(fill=tk.X, pady=(0, 5))
        
        label = tk.Label(row, text=f"{emoji} {title}", 
                        bg=self.colors['card'], fg=self.colors['text'],
                        font=('Segoe UI', 10, 'bold'))
        label.pack(anchor=tk.W)

    def create_footer(self, parent):
        """Create footer with action buttons"""
        footer = tk.Frame(parent, bg=self.colors['bg'])
        
        # Button container
        button_container = tk.Frame(footer, bg=self.colors['bg'])
        button_container.pack(expand=True)
        
        # Cancel button
        cancel_btn = tk.Button(button_container, text="✕ Cancel", 
                              command=self.destroy,
                              bg=self.colors['secondary'], fg='white',
                              font=('Segoe UI', 10), relief='flat', bd=0,
                              padx=20, pady=10, cursor='hand2')
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))
        
        # Save button with gradient-like effect
        save_btn = tk.Button(button_container, text="💾 Save Settings", 
                            command=self.save_and_close,
                            bg=self.settings_colors['success'], fg='white',
                            font=('Segoe UI', 10, 'bold'), relief='flat', bd=0,
                            padx=25, pady=10, cursor='hand2')
        save_btn.pack(side=tk.RIGHT)
        
        # Add hover effects
        def save_hover_enter(e):
            save_btn.configure(bg='#059669')  # Darker green
        def save_hover_leave(e):
            save_btn.configure(bg=self.settings_colors['success'])
        
        def cancel_hover_enter(e):
            cancel_btn.configure(bg='#475569')  # Darker gray
        def cancel_hover_leave(e):
            cancel_btn.configure(bg=self.colors['secondary'])
        
        save_btn.bind("<Enter>", save_hover_enter)
        save_btn.bind("<Leave>", save_hover_leave)
        cancel_btn.bind("<Enter>", cancel_hover_enter)
        cancel_btn.bind("<Leave>", cancel_hover_leave)
        
        return footer

    def generate_random_seed(self):
        """Generate a random seed"""
        import random
        self.seed_var.set(str(random.randint(1, 999999)))

    def save_and_close(self):
        """Save settings and close window"""
        # Update settings with new values
        self.current_settings["model"] = self.model_var.get()
        self.current_settings["image_size"] = self.image_size_var.get()
        self.current_settings["num_inference_steps"] = self.inf_steps_var.get()
        self.current_settings["num_images"] = self.num_images_var.get()
        self.current_settings["enable_safety_checker"] = self.safety_checker_var.get()
        
        seed_value = self.seed_var.get().strip()
        self.current_settings["seed"] = int(seed_value) if seed_value and seed_value.isdigit() else None

        self.save_callback(self.current_settings)
        self.destroy()


if __name__ == "__main__":
    app = ModernImageGeneratorGUI()
    app.mainloop() 