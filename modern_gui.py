"""
Modern GUI for FLUX.1 Kontext Pro Image Generator
A comprehensive, user-friendly interface with all FLUX.1 Kontext Pro parameters and features.
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, filedialog, messagebox
import threading
import asyncio
from datetime import datetime
from typing import Optional, Callable, List
from pathlib import Path
import logging

try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    DRAG_DROP_AVAILABLE = True
except ImportError:
    DRAG_DROP_AVAILABLE = False
    print("Warning: tkinterdnd2 not available. Drag and drop functionality will be disabled.")

from generator import FluxKontextClient, GenerationRequest, EditingRequest, SafetyTolerance, OutputFormat, AspectRatio
from settings import Config<PERSON>anager
from image_manager import ImageManager


class ModernFluxGUI(TkinterDnD.Tk if DRAG_DROP_AVAILABLE else tk.Tk):
    """
    Modern GUI application for FLUX.1 Kontext Pro Image Generator.
    Features comprehensive parameter controls, image preview, and progress monitoring.
    """
    
    def __init__(self):
        """Initialize the modern GUI application."""
        super().__init__()
        
        # Initialize core components
        self.config_manager = ConfigManager()
        self.image_manager = ImageManager()
        self.flux_client = None
        self.current_generation_task = None
        
        # Setup window
        self.setup_window()
        self.setup_styles()
        
        # Create GUI components
        self.create_widgets()
        
        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()
        
        # Initialize client
        self.initialize_client()
        
        # Center window
        self.center_window()
    
    def setup_window(self):
        """Setup main window properties."""
        config = self.config_manager.get_config()
        
        self.title("🎨 FLUX.1 Kontext Pro Image Generator")
        self.geometry(f"{config.window_width}x{config.window_height}")
        self.minsize(1000, 700)
        
        # Enhanced modern color scheme with gradients and depth
        self.colors = {
            'bg': '#f0f2f5',  # Softer background
            'bg_gradient': '#e8ecf0',  # Gradient background
            'card': '#ffffff',
            'card_hover': '#f8f9fa',
            'primary': '#4285f4',  # Google Blue
            'primary_hover': '#3367d6',
            'primary_light': '#e3f2fd',
            'secondary': '#5f6368',
            'secondary_light': '#f1f3f4',
            'success': '#34a853',  # Google Green
            'success_light': '#e8f5e8',
            'warning': '#fbbc04',  # Google Yellow
            'warning_light': '#fef7e0',
            'danger': '#ea4335',  # Google Red
            'danger_light': '#fce8e6',
            'text': '#202124',
            'text_secondary': '#5f6368',
            'text_muted': '#9aa0a6',
            'border': '#dadce0',
            'border_light': '#f1f3f4',
            'input_bg': '#ffffff',
            'input_border': '#dadce0',
            'input_focus': '#4285f4',
            'shadow': '#00000010',
            'shadow_hover': '#00000020',
            'accent': '#9c27b0',  # Purple accent
            'accent_light': '#f3e5f5'
        }
        
        self.configure(bg=self.colors['bg'])
        
        # Enable drag and drop
        self.setup_drag_drop()
    
    def setup_styles(self):
        """Setup enhanced modern ttk styles with visual effects."""
        style = ttk.Style()
        
        # Set theme to clam for better customization
        style.theme_use('clam')
        
        # Configure modern button style with hover effects
        style.configure(
            'Modern.TButton',
            padding=(16, 10),
            font=('Segoe UI', 10),
            borderwidth=0,
            focuscolor='none',
            background=self.colors['card'],
            foreground=self.colors['text'],
            relief='flat'
        )
        
        style.map(
            'Modern.TButton',
            background=[('active', self.colors['card_hover']),
                       ('pressed', self.colors['border'])],
            foreground=[('active', self.colors['text']),
                       ('pressed', self.colors['text'])],
            relief=[('pressed', 'flat'), ('!pressed', 'flat')]
        )
        
        # Configure primary button style with gradient effect
        style.configure(
            'Primary.TButton',
            padding=(18, 12),
            font=('Segoe UI', 11, 'bold'),
            borderwidth=0,
            focuscolor='none',
            background=self.colors['primary'],
            foreground='white',
            relief='flat'
        )
        
        style.map(
            'Primary.TButton',
            background=[('active', self.colors['primary_hover']),
                       ('pressed', self.colors['primary_hover'])],
            foreground=[('active', 'white'),
                       ('pressed', 'white')],
            relief=[('pressed', 'flat'), ('!pressed', 'flat')]
        )
        
        # Configure success button style
        style.configure(
            'Success.TButton',
            padding=(16, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            focuscolor='none',
            background=self.colors['success'],
            foreground='white',
            relief='flat'
        )
        
        style.map(
            'Success.TButton',
            background=[('active', '#2e7d32'),
                       ('pressed', '#1b5e20')],
            relief=[('pressed', 'flat'), ('!pressed', 'flat')]
        )
        
        # Configure warning button style
        style.configure(
            'Warning.TButton',
            padding=(16, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            focuscolor='none',
            background=self.colors['warning'],
            foreground=self.colors['text'],
            relief='flat'
        )
        
        # Configure danger button style
        style.configure(
            'Danger.TButton',
            padding=(16, 10),
            font=('Segoe UI', 10, 'bold'),
            borderwidth=0,
            focuscolor='none',
            background=self.colors['danger'],
            foreground='white',
            relief='flat'
        )
        
        # Configure enhanced frame styles
        style.configure(
            'Card.TFrame',
            background=self.colors['card'],
            borderwidth=1,
            relief='flat',
            lightcolor=self.colors['border_light'],
            darkcolor=self.colors['border']
        )
        
        style.configure(
            'Elevated.TFrame',
            background=self.colors['card'],
            borderwidth=0,
            relief='flat'
        )
        
        # Configure notebook styles
        style.configure(
            'TNotebook',
            background=self.colors['bg'],
            borderwidth=0,
            tabmargins=[0, 0, 0, 0]
        )
        
        style.configure(
            'TNotebook.Tab',
            padding=(20, 12),
            font=('Segoe UI', 10, 'bold'),
            background=self.colors['secondary_light'],
            foreground=self.colors['text_secondary'],
            borderwidth=0,
            focuscolor='none'
        )
        
        style.map(
            'TNotebook.Tab',
            background=[('selected', self.colors['card']),
                       ('active', self.colors['card_hover'])],
            foreground=[('selected', self.colors['primary']),
                       ('active', self.colors['text'])],
            expand=[('selected', [1, 1, 1, 0])]
        )
        
        # Configure enhanced label styles
        style.configure(
            'Heading.TLabel',
            font=('Segoe UI', 14, 'bold'),
            background=self.colors['card'],
            foreground=self.colors['text']
        )
        
        style.configure(
            'Title.TLabel',
            font=('Segoe UI', 18, 'bold'),
            background=self.colors['card'],
            foreground=self.colors['text']
        )
        
        style.configure(
            'Subtitle.TLabel',
            font=('Segoe UI', 12),
            background=self.colors['card'],
            foreground=self.colors['text_secondary']
        )
        
        style.configure(
            'Card.TLabel',
            background=self.colors['card'],
            foreground=self.colors['text'],
            font=('Segoe UI', 9)
        )
        
        style.configure(
            'Muted.TLabel',
            background=self.colors['card'],
            foreground=self.colors['text_muted'],
            font=('Segoe UI', 9)
        )
        
        # Configure entry styles
        style.configure(
            'Modern.TEntry',
            padding=(12, 8),
            font=('Segoe UI', 10),
            borderwidth=1,
            relief='solid',
            fieldbackground=self.colors['input_bg'],
            bordercolor=self.colors['input_border'],
            focuscolor=self.colors['input_focus']
        )
        
        style.map(
            'Modern.TEntry',
            bordercolor=[('focus', self.colors['input_focus'])],
            lightcolor=[('focus', self.colors['input_focus'])],
            darkcolor=[('focus', self.colors['input_focus'])]
        )
        
        # Configure combobox styles
        style.configure(
            'Modern.TCombobox',
            padding=(12, 8),
            font=('Segoe UI', 10),
            borderwidth=1,
            relief='solid',
            fieldbackground=self.colors['input_bg'],
            bordercolor=self.colors['input_border'],
            focuscolor=self.colors['input_focus'],
            arrowcolor=self.colors['text_secondary']
        )
        
        # Configure scale styles
        style.configure(
            'Modern.TScale',
            background=self.colors['card'],
            troughcolor=self.colors['secondary_light'],
            borderwidth=0,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )
        
        # Configure progressbar styles
        style.configure(
            'Modern.TProgressbar',
            background=self.colors['primary'],
            troughcolor=self.colors['secondary_light'],
            borderwidth=0,
            lightcolor=self.colors['primary'],
            darkcolor=self.colors['primary']
        )
        
        # Configure separator styles
        style.configure(
            'Modern.TSeparator',
            background=self.colors['border_light']
        )
        
        # Configure elevated frame style for header
        style.configure(
            'Elevated.TFrame',
            background=self.colors['card'],
            relief='flat',
            borderwidth=1,
            lightcolor=self.colors['card'],
            darkcolor=self.colors['border_light']
        )
        
        # Configure modern notebook style
        style.configure(
            'Modern.TNotebook',
            background=self.colors['bg'],
            borderwidth=0,
            tabmargins=[2, 5, 2, 0]
        )
        
        style.configure(
            'Modern.TNotebook.Tab',
            padding=(25, 15),
            font=('Segoe UI', 11, 'bold'),
            background=self.colors['secondary_light'],
            foreground=self.colors['text_secondary'],
            borderwidth=0,
            focuscolor='none',
            relief='flat'
        )
        
        style.map(
            'Modern.TNotebook.Tab',
            background=[('selected', self.colors['card']),
                       ('active', self.colors['card_hover'])],
            foreground=[('selected', self.colors['primary']),
                       ('active', self.colors['text'])],
            expand=[('selected', [1, 1, 1, 0])],
            relief=[('selected', 'flat'), ('active', 'flat')]
        )
    
    def create_widgets(self):
        """Create and layout all GUI widgets."""
        # Main container with padding
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Create main sections
        self.create_header(main_container)
        self.create_main_content(main_container)
        self.create_status_bar(main_container)
    
    def create_header(self, parent):
        """Create the enhanced header section with modern styling."""
        # Main header container with gradient-like effect
        header_container = ttk.Frame(parent)
        header_container.pack(fill=tk.X, pady=(0, 25))
        
        # Top header with elevated card style
        header_frame = ttk.Frame(header_container, style='Elevated.TFrame')
        header_frame.pack(fill=tk.X)
        
        # Add subtle shadow effect with a thin separator
        shadow_frame = ttk.Frame(header_container, height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 3))
        shadow_frame.configure(style='Card.TFrame')
        
        # Title and controls container
        title_frame = ttk.Frame(header_frame, style='Elevated.TFrame')
        title_frame.pack(fill=tk.X, padx=25, pady=20)
        
        # Left side - Title and subtitle
        title_container = ttk.Frame(title_frame, style='Elevated.TFrame')
        title_container.pack(side=tk.LEFT, fill=tk.Y)
        
        # Main title with enhanced typography
        title_label = ttk.Label(
            title_container,
            text="🎨 FLUX.1 Kontext Pro",
            style='Title.TLabel'
        )
        title_label.pack(anchor=tk.W)
        
        # Subtitle
        subtitle_label = ttk.Label(
            title_container,
            text="AI-Powered Image Generator",
            style='Subtitle.TLabel'
        )
        subtitle_label.pack(anchor=tk.W, pady=(2, 0))
        
        # Right side - Status and controls
        controls_frame = ttk.Frame(title_frame, style='Elevated.TFrame')
        controls_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # API status with enhanced styling
        status_container = ttk.Frame(controls_frame, style='Elevated.TFrame')
        status_container.pack(side=tk.RIGHT, padx=(0, 15))
        
        self.api_status_label = ttk.Label(
            status_container,
            text="🔴 API Disconnected",
            style='Muted.TLabel',
            font=('Segoe UI', 9, 'bold')
        )
        self.api_status_label.pack()
        
        # Button container with spacing
        button_container = ttk.Frame(controls_frame, style='Elevated.TFrame')
        button_container.pack(side=tk.RIGHT)
        
        # Help button with icon
        help_btn = ttk.Button(
            button_container,
            text="❓ Help",
            style='Modern.TButton',
            command=self.show_shortcuts_help
        )
        help_btn.pack(side=tk.RIGHT, padx=(0, 8))
        
        # Settings button with enhanced styling
        settings_btn = ttk.Button(
            button_container,
            text="⚙️ Settings",
            style='Modern.TButton',
            command=self.open_settings
        )
        settings_btn.pack(side=tk.RIGHT, padx=(0, 8))
        
        # Add hover effects to buttons
        self.add_button_hover_effects(help_btn)
        self.add_button_hover_effects(settings_btn)
    
    def add_button_hover_effects(self, button):
        """Add hover effects to buttons for better interactivity."""
        def on_enter(event):
            button.configure(cursor='hand2')
        
        def on_leave(event):
            button.configure(cursor='')
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
    
    def create_main_content(self, parent):
        """Create the enhanced main content area with modern tabs and layout."""
        # Main content container with enhanced styling
        content_container = ttk.Frame(parent, style='Elevated.TFrame')
        content_container.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Create enhanced notebook for tabs
        self.notebook = ttk.Notebook(content_container, style='Modern.TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Create generation tab with enhanced styling
        self.generation_tab = ttk.Frame(self.notebook, style='Card.TFrame')
        self.notebook.add(self.generation_tab, text="🎨 Generate Images")

        # Create editing tab with enhanced styling
        self.editing_tab = ttk.Frame(self.notebook, style='Card.TFrame')
        self.notebook.add(self.editing_tab, text="✏️ Edit Images")

        # Setup generation tab
        self.create_generation_content(self.generation_tab)

        # Setup editing tab
        self.create_editing_content(self.editing_tab)

        # Bind tab change event
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
        
        # Add subtle animation effect for tab switching
        self.add_tab_animations()
    
    def add_tab_animations(self):
        """Add subtle animations for tab switching."""
        def on_tab_change(event):
            # Add a subtle fade effect by temporarily changing opacity
            selected_tab = self.notebook.select()
            if selected_tab:
                tab_widget = self.notebook.nametowidget(selected_tab)
                # Simple visual feedback
                tab_widget.configure(relief='flat')
                self.after(100, lambda: tab_widget.configure(relief='flat'))
        
        self.notebook.bind('<<NotebookTabChanged>>', on_tab_change)

    def create_generation_content(self, parent):
        """Create the enhanced content for the generation tab."""
        # Main container with padding
        main_container = ttk.Frame(parent, style='Card.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # Create enhanced paned window for resizable sections
        paned_window = ttk.PanedWindow(main_container, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left panel - Controls with enhanced styling
        left_panel = ttk.Frame(paned_window, style='Elevated.TFrame')
        paned_window.add(left_panel, weight=2)

        # Right panel - Output and Images with enhanced styling
        right_panel = ttk.Frame(paned_window, style='Elevated.TFrame')
        paned_window.add(right_panel, weight=3)

        # Create control sections with enhanced styling
        self.create_prompt_section(left_panel)
        self.create_parameters_section(left_panel)
        self.create_generation_controls(left_panel)

        # Create output sections with enhanced styling
        self.create_progress_section(right_panel)
        self.create_image_preview_section(right_panel)
        self.create_log_section(right_panel)

    def create_editing_content(self, parent):
        """Create the content for the editing tab."""
        # Main container with padding
        main_container = ttk.Frame(parent, style='Card.TFrame')
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # Create paned window for resizable sections
        paned_window = ttk.PanedWindow(main_container, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # Left panel - Editing Controls with scrollable frame
        left_panel_container = ttk.Frame(paned_window, style='Elevated.TFrame')
        paned_window.add(left_panel_container, weight=2)
        
        # Create scrollable canvas for left panel
        canvas = tk.Canvas(left_panel_container, highlightthickness=0)
        scrollbar = ttk.Scrollbar(left_panel_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind mousewheel to canvas for scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Right panel - Output and Images
        right_panel = ttk.Frame(paned_window, style='Elevated.TFrame')
        paned_window.add(right_panel, weight=3)

        # Create sections for left panel (now in scrollable frame)
        self.create_image_upload_section(scrollable_frame)
        self.create_editing_prompt_section(scrollable_frame)
        self.create_editing_parameters_section(scrollable_frame)
        self.create_editing_controls(scrollable_frame)

        # Create output sections (reuse from generation)
        self.create_editing_progress_section(right_panel)
        self.create_editing_preview_section(right_panel)
        self.create_editing_log_section(right_panel)

    def on_tab_changed(self, event):
        """Handle tab change events."""
        selected_tab = event.widget.tab('current')['text']
        if "Generate" in selected_tab:
            self.log_message("Switched to image generation mode")
        elif "Edit" in selected_tab:
            self.log_message("Switched to image editing mode")

    def create_image_upload_section(self, parent):
        """Create the image upload section for editing."""
        upload_frame = ttk.LabelFrame(parent, text="Select Image to Edit", padding=15)
        upload_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Upload from file
        upload_file_frame = ttk.Frame(upload_frame)
        upload_file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(upload_file_frame, text="Upload Image File:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        file_button_frame = ttk.Frame(upload_file_frame)
        file_button_frame.pack(fill=tk.X, pady=(5, 0))

        self.upload_file_btn = ttk.Button(
            file_button_frame,
            text="📁 Choose Image File",
            style='Modern.TButton',
            command=self.upload_image_file
        )
        self.upload_file_btn.pack(side=tk.LEFT)

        self.uploaded_file_label = ttk.Label(
            file_button_frame,
            text="No file selected",
            font=('Segoe UI', 9),
            foreground=self.colors['text_muted']
        )
        self.uploaded_file_label.pack(side=tk.LEFT, padx=(10, 0))

        # Separator
        ttk.Separator(upload_frame, orient='horizontal').pack(fill=tk.X, pady=10)

        # Select from generated images
        generated_frame = ttk.Frame(upload_frame)
        generated_frame.pack(fill=tk.X)

        ttk.Label(generated_frame, text="Or Select from Generated Images:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.select_generated_btn = ttk.Button(
            generated_frame,
            text="🖼️ Select from Gallery",
            style='Modern.TButton',
            command=self.select_from_generated,
            state=tk.DISABLED  # Initially disabled
        )
        self.select_generated_btn.pack(anchor=tk.W, pady=(5, 0))

        # Current image info
        self.current_image_frame = ttk.Frame(upload_frame)
        self.current_image_frame.pack(fill=tk.X, pady=(10, 0))

        self.current_image_info = ttk.Label(
            self.current_image_frame,
            text="",
            font=('Segoe UI', 9),
            foreground=self.colors['text']
        )
        self.current_image_info.pack(anchor=tk.W)

        # Store current image for editing
        self.current_editing_image = None
        self.current_editing_image_url = None
        
        # Enable drag and drop for image upload
        self.enable_drag_drop_for_upload(upload_frame)

    def create_editing_prompt_section(self, parent):
        """Create the editing prompt input section."""
        prompt_frame = ttk.LabelFrame(parent, text="Editing Instructions", padding=15)
        prompt_frame.pack(fill=tk.X, padx=15, pady=10)

        # Editing prompt text area
        ttk.Label(prompt_frame, text="Describe what you want to change:", font=('Segoe UI', 10)).pack(anchor=tk.W, pady=(0, 5))

        self.editing_prompt_text = tk.Text(
            prompt_frame,
            height=3,
            wrap=tk.WORD,
            font=('Segoe UI', 10),
            bg=self.colors['input_bg'],
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid',
            highlightthickness=1,
            highlightcolor=self.colors['primary']
        )
        self.editing_prompt_text.pack(fill=tk.X, pady=(0, 10))

        # Quick editing prompts
        quick_edits_frame = ttk.Frame(prompt_frame)
        quick_edits_frame.pack(fill=tk.X)

        ttk.Label(quick_edits_frame, text="Quick edits:", font=('Segoe UI', 9)).pack(anchor=tk.W, pady=(0, 5))

        quick_edits = [
            "Change the background to a sunset",
            "Add a cat to the scene",
            "Make it look like a painting",
            "Remove the background"
        ]

        for i, edit in enumerate(quick_edits):
            btn = ttk.Button(
                quick_edits_frame,
                text=edit,
                style='Modern.TButton',
                command=lambda e=edit: self.set_editing_prompt(e)
            )
            btn.pack(side=tk.LEFT, padx=(0, 5) if i < len(quick_edits)-1 else 0, pady=2)
            if i == 1:  # Break to new line after 2 buttons
                quick_edits_frame = ttk.Frame(prompt_frame)
                quick_edits_frame.pack(fill=tk.X, pady=(5, 0))

    def create_editing_parameters_section(self, parent):
        """Create the editing parameters control section."""
        params_frame = ttk.LabelFrame(parent, text="Editing Parameters", padding=15)
        params_frame.pack(fill=tk.X, padx=15, pady=10)

        # Editing seed control
        seed_frame = ttk.Frame(params_frame)
        seed_frame.pack(fill=tk.X, pady=5)

        ttk.Label(seed_frame, text="Seed:", width=15).pack(side=tk.LEFT)
        self.editing_seed_var = tk.StringVar()
        seed_entry = ttk.Entry(seed_frame, textvariable=self.editing_seed_var, width=15)
        seed_entry.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Button(
            seed_frame,
            text="Random",
            style='Modern.TButton',
            command=self.randomize_editing_seed
        ).pack(side=tk.LEFT)

        # Editing Guidance Scale
        guidance_frame = ttk.Frame(params_frame)
        guidance_frame.pack(fill=tk.X, pady=5)

        ttk.Label(guidance_frame, text="Guidance Scale:", width=15).pack(side=tk.LEFT)
        self.editing_guidance_var = tk.DoubleVar(value=3.5)
        guidance_scale = ttk.Scale(
            guidance_frame,
            from_=0.0,
            to=20.0,
            variable=self.editing_guidance_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        guidance_scale.pack(side=tk.LEFT, padx=5)

        self.editing_guidance_label = ttk.Label(guidance_frame, text="3.5")
        self.editing_guidance_label.pack(side=tk.LEFT, padx=5)
        guidance_scale.configure(command=self.update_editing_guidance_label)

        # Number of edited images
        num_images_frame = ttk.Frame(params_frame)
        num_images_frame.pack(fill=tk.X, pady=5)

        ttk.Label(num_images_frame, text="Number of Images:", width=15).pack(side=tk.LEFT)
        self.editing_num_images_var = tk.IntVar(value=1)
        num_images_spin = ttk.Spinbox(
            num_images_frame,
            from_=1,
            to=10,
            textvariable=self.editing_num_images_var,
            width=10
        )
        num_images_spin.pack(side=tk.LEFT, padx=5)

        # Editing Safety Tolerance
        safety_frame = ttk.Frame(params_frame)
        safety_frame.pack(fill=tk.X, pady=5)

        ttk.Label(safety_frame, text="Safety Tolerance:", width=15).pack(side=tk.LEFT)
        self.editing_safety_var = tk.StringVar(value="6")
        safety_combo = ttk.Combobox(
            safety_frame,
            textvariable=self.editing_safety_var,
            values=[e.value for e in SafetyTolerance],
            state="readonly",
            width=12
        )
        safety_combo.pack(side=tk.LEFT, padx=5)

        # Editing Output Format
        format_frame = ttk.Frame(params_frame)
        format_frame.pack(fill=tk.X, pady=5)

        ttk.Label(format_frame, text="Output Format:", width=15).pack(side=tk.LEFT)
        self.editing_format_var = tk.StringVar(value="png")
        format_combo = ttk.Combobox(
            format_frame,
            textvariable=self.editing_format_var,
            values=[e.value for e in OutputFormat],
            state="readonly",
            width=12
        )
        format_combo.pack(side=tk.LEFT, padx=5)

    def create_editing_controls(self, parent):
        """Create editing control buttons."""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=15, pady=15)

        # Edit button
        self.edit_btn = ttk.Button(
            controls_frame,
            text="✏️ Edit Image",
            style='Primary.TButton',
            command=self.start_editing,
            state=tk.DISABLED  # Initially disabled until image is selected
        )
        self.edit_btn.pack(fill=tk.X, pady=(0, 10))

        # Cancel editing button
        self.cancel_edit_btn = ttk.Button(
            controls_frame,
            text="❌ Cancel Editing",
            style='Modern.TButton',
            command=self.cancel_editing,
            state=tk.DISABLED
        )
        self.cancel_edit_btn.pack(fill=tk.X)

    def create_editing_progress_section(self, parent):
        """Create the editing progress monitoring section."""
        progress_frame = ttk.LabelFrame(parent, text="Editing Progress", padding=15)
        progress_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # Progress bar
        self.editing_progress_var = tk.DoubleVar()
        self.editing_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.editing_progress_var,
            mode='indeterminate'
        )
        self.editing_progress_bar.pack(fill=tk.X, pady=(0, 10))

        # Status label
        self.editing_status_label = ttk.Label(
            progress_frame,
            text="Select an image to start editing",
            font=('Segoe UI', 9)
        )
        self.editing_status_label.pack()

    def create_editing_preview_section(self, parent):
        """Create the editing preview section."""
        preview_frame = ttk.LabelFrame(parent, text="Edited Images", padding=15)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # Create scrollable frame for edited images
        canvas = tk.Canvas(preview_frame, bg=self.colors['card'])
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=canvas.yview)
        self.editing_scrollable_frame = ttk.Frame(canvas)

        self.editing_scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.editing_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initial placeholder
        self.editing_preview_placeholder = ttk.Label(
            self.editing_scrollable_frame,
            text="Edited images will appear here",
            font=('Segoe UI', 10),
            foreground=self.colors['text_muted']
        )
        self.editing_preview_placeholder.pack(expand=True, pady=50)

        # Store references
        self.editing_preview_canvas = canvas
        self.editing_preview_scrollbar = scrollbar
        self.editing_image_widgets = []  # Store edited image widget references

    def create_editing_log_section(self, parent):
        """Create the editing log output section."""
        log_frame = ttk.LabelFrame(parent, text="Editing Log", padding=10)
        log_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        # Log text area (reuse the main log for now)
        self.editing_log_text = scrolledtext.ScrolledText(
            log_frame,
            height=6,
            wrap=tk.WORD,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid'
        )
        self.editing_log_text.pack(fill=tk.X)
    
    def create_prompt_section(self, parent):
        """Create the prompt input section."""
        prompt_frame = ttk.LabelFrame(parent, text="Image Prompt", padding=15)
        prompt_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        # Prompt text area
        self.prompt_text = tk.Text(
            prompt_frame,
            height=4,
            wrap=tk.WORD,
            font=('Segoe UI', 10),
            bg=self.colors['input_bg'],
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid',
            highlightthickness=1,
            highlightcolor=self.colors['primary']
        )
        self.prompt_text.pack(fill=tk.X, pady=(0, 10))
        
        # Quick prompt buttons
        quick_prompts_frame = ttk.Frame(prompt_frame)
        quick_prompts_frame.pack(fill=tk.X)
        
        quick_prompts = [
            "A majestic landscape at sunset",
            "Portrait of a wise old wizard",
            "Futuristic city skyline",
            "Abstract art with vibrant colors"
        ]
        
        for i, prompt in enumerate(quick_prompts):
            btn = ttk.Button(
                quick_prompts_frame,
                text=prompt[:20] + "...",
                style='Modern.TButton',
                command=lambda p=prompt: self.set_prompt(p)
            )
            btn.pack(side=tk.LEFT, padx=(0, 5) if i < len(quick_prompts)-1 else 0)
    
    def create_parameters_section(self, parent):
        """Create the parameters control section."""
        params_frame = ttk.LabelFrame(parent, text="Generation Parameters", padding=15)
        params_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Create parameter controls in a grid
        self.create_parameter_controls(params_frame)
    
    def create_parameter_controls(self, parent):
        """Create individual parameter controls."""
        # Seed control
        seed_frame = ttk.Frame(parent)
        seed_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(seed_frame, text="Seed:", width=15).pack(side=tk.LEFT)
        self.seed_var = tk.StringVar()
        seed_entry = ttk.Entry(seed_frame, textvariable=self.seed_var, width=15)
        seed_entry.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(
            seed_frame,
            text="Random",
            style='Modern.TButton',
            command=self.randomize_seed
        ).pack(side=tk.LEFT)
        
        # Guidance Scale
        guidance_frame = ttk.Frame(parent)
        guidance_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(guidance_frame, text="Guidance Scale:", width=15).pack(side=tk.LEFT)
        self.guidance_var = tk.DoubleVar(value=3.5)
        guidance_scale = ttk.Scale(
            guidance_frame,
            from_=0.0,
            to=20.0,
            variable=self.guidance_var,
            orient=tk.HORIZONTAL,
            length=200
        )
        guidance_scale.pack(side=tk.LEFT, padx=5)
        
        self.guidance_label = ttk.Label(guidance_frame, text="3.5")
        self.guidance_label.pack(side=tk.LEFT, padx=5)
        guidance_scale.configure(command=self.update_guidance_label)
        
        # Number of Images
        num_images_frame = ttk.Frame(parent)
        num_images_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(num_images_frame, text="Number of Images:", width=15).pack(side=tk.LEFT)
        self.num_images_var = tk.IntVar(value=1)
        num_images_spin = ttk.Spinbox(
            num_images_frame,
            from_=1,
            to=10,
            textvariable=self.num_images_var,
            width=10
        )
        num_images_spin.pack(side=tk.LEFT, padx=5)
        
        # Safety Tolerance
        safety_frame = ttk.Frame(parent)
        safety_frame.pack(fill=tk.X, pady=5)

        ttk.Label(safety_frame, text="Safety Tolerance:", width=15).pack(side=tk.LEFT)
        self.safety_var = tk.StringVar(value="6")  # Default to maximum permissive
        safety_combo = ttk.Combobox(
            safety_frame,
            textvariable=self.safety_var,
            values=[e.value for e in SafetyTolerance],
            state="readonly",
            width=12
        )
        safety_combo.pack(side=tk.LEFT, padx=5)

        # Output Format
        format_frame = ttk.Frame(parent)
        format_frame.pack(fill=tk.X, pady=5)

        ttk.Label(format_frame, text="Output Format:", width=15).pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="png")  # Default to PNG
        format_combo = ttk.Combobox(
            format_frame,
            textvariable=self.format_var,
            values=[e.value for e in OutputFormat],
            state="readonly",
            width=12
        )
        format_combo.pack(side=tk.LEFT, padx=5)
        
        # Aspect Ratio
        aspect_frame = ttk.Frame(parent)
        aspect_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(aspect_frame, text="Aspect Ratio:", width=15).pack(side=tk.LEFT)
        self.aspect_var = tk.StringVar(value="1:1")
        aspect_combo = ttk.Combobox(
            aspect_frame,
            textvariable=self.aspect_var,
            values=[e.value for e in AspectRatio],
            state="readonly",
            width=12
        )
        aspect_combo.pack(side=tk.LEFT, padx=5)
    
    def create_generation_controls(self, parent):
        """Create generation control buttons."""
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # Generate button
        self.generate_btn = ttk.Button(
            controls_frame,
            text="🚀 Generate Images",
            style='Primary.TButton',
            command=self.start_generation
        )
        self.generate_btn.pack(fill=tk.X, pady=(0, 10))
        
        # Cancel button
        self.cancel_btn = ttk.Button(
            controls_frame,
            text="❌ Cancel Generation",
            style='Modern.TButton',
            command=self.cancel_generation,
            state=tk.DISABLED
        )
        self.cancel_btn.pack(fill=tk.X)
    
    def create_progress_section(self, parent):
        """Create the progress monitoring section."""
        progress_frame = ttk.LabelFrame(parent, text="Generation Progress", padding=15)
        progress_frame.pack(fill=tk.X, padx=15, pady=(15, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            mode='indeterminate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Status label
        self.status_label = ttk.Label(
            progress_frame,
            text="Ready to generate images",
            font=('Segoe UI', 9)
        )
        self.status_label.pack()
    
    def create_image_preview_section(self, parent):
        """Create the image preview section."""
        preview_frame = ttk.LabelFrame(parent, text="Generated Images", padding=15)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # Create scrollable frame for images
        canvas = tk.Canvas(preview_frame, bg=self.colors['card'])
        scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Initial placeholder
        self.preview_placeholder = ttk.Label(
            self.scrollable_frame,
            text="Generated images will appear here",
            font=('Segoe UI', 10),
            foreground=self.colors['text_muted']
        )
        self.preview_placeholder.pack(expand=True, pady=50)

        # Store references
        self.preview_canvas = canvas
        self.preview_scrollbar = scrollbar
        self.image_widgets = []  # Store image widget references
    
    def create_log_section(self, parent):
        """Create the log output section."""
        log_frame = ttk.LabelFrame(parent, text="Generation Log", padding=10)
        log_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=8,
            wrap=tk.WORD,
            font=('Consolas', 9),
            bg='#f8f9fa',
            fg=self.colors['text'],
            borderwidth=1,
            relief='solid'
        )
        self.log_text.pack(fill=tk.X)
    
    def create_status_bar(self, parent):
        """Create the enhanced status bar with modern styling."""
        # Status bar container with elevated styling
        status_container = ttk.Frame(parent, style='Elevated.TFrame')
        status_container.pack(fill=tk.X, pady=(15, 0))
        
        # Main status frame with padding
        status_frame = ttk.Frame(status_container, style='Elevated.TFrame')
        status_frame.pack(fill=tk.X, padx=20, pady=12)
        
        # Left side - Status with icon
        status_left = ttk.Frame(status_frame, style='Elevated.TFrame')
        status_left.pack(side=tk.LEFT, fill=tk.Y)
        
        # Status icon and text
        self.status_icon_label = ttk.Label(
            status_left,
            text="✅",
            font=('Segoe UI', 10),
            style='Muted.TLabel'
        )
        self.status_icon_label.pack(side=tk.LEFT, padx=(0, 8))
        
        self.status_bar_label = ttk.Label(
            status_left,
            text="Ready to generate amazing images",
            font=('Segoe UI', 10, 'normal'),
            style='Muted.TLabel'
        )
        self.status_bar_label.pack(side=tk.LEFT)
        
        # Right side - Version and additional info
        status_right = ttk.Frame(status_frame, style='Elevated.TFrame')
        status_right.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Version info with enhanced styling
        version_label = ttk.Label(
            status_right,
            text="🚀 FLUX.1 Kontext Pro v1.0",
            font=('Segoe UI', 9, 'bold'),
            style='Muted.TLabel'
        )
        version_label.pack(side=tk.RIGHT)
        
        # Add subtle separator line above status bar
        separator = ttk.Separator(status_container, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 0))

    def initialize_client(self):
        """Initialize the FLUX client with callbacks."""
        try:
            config = self.config_manager.get_config()

            self.flux_client = FluxKontextClient(
                api_key=config.api_key,
                progress_callback=self.update_progress,
                log_callback=self.log_message
            )

            if config.api_key:
                self.api_status_label.config(text="🟢 API Connected")
                self.log_message("FLUX.1 Kontext Pro client initialized successfully")
            else:
                self.api_status_label.config(text="🟡 API Key Required")
                self.log_message("Warning: No API key configured. Please check settings.")

        except Exception as e:
            self.api_status_label.config(text="🔴 API Error")
            self.log_message(f"Error initializing client: {str(e)}")

    def center_window(self):
        """Center the window on screen."""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f'{width}x{height}+{x}+{y}')

    def set_prompt(self, prompt: str):
        """Set the prompt text."""
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, prompt)

    def randomize_seed(self):
        """Generate a random seed."""
        import random
        seed = random.randint(0, 2**32 - 1)
        self.seed_var.set(str(seed))

    def update_guidance_label(self, value):
        """Update the guidance scale label."""
        self.guidance_label.config(text=f"{float(value):.1f}")

    def log_message(self, message: str):
        """Add a message to the log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.update_idletasks()

    def update_progress(self, message: str):
        """Update the progress status with enhanced visual feedback."""
        # Update status text
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
        
        self.status_bar_label.config(text=message)
        
        # Update status icon based on message content
        if hasattr(self, 'status_icon_label'):
            if "error" in message.lower() or "failed" in message.lower():
                self.status_icon_label.config(text="❌")
            elif "generating" in message.lower() or "processing" in message.lower():
                self.status_icon_label.config(text="⚡")
            elif "completed" in message.lower() or "success" in message.lower():
                self.status_icon_label.config(text="✅")
            elif "ready" in message.lower():
                self.status_icon_label.config(text="✅")
            else:
                self.status_icon_label.config(text="ℹ️")
        
        self.update_idletasks()

    def start_generation(self):
        """Start the image generation process."""
        # Validate inputs
        prompt = self.prompt_text.get(1.0, tk.END).strip()
        if not prompt:
            messagebox.showwarning("Warning", "Please enter a prompt for image generation.")
            return

        if not self.flux_client:
            messagebox.showerror("Error", "FLUX client not initialized. Please check your API key.")
            return

        # Prepare generation request
        try:
            seed_str = self.seed_var.get().strip()
            seed = int(seed_str) if seed_str and seed_str.isdigit() else None

            request = GenerationRequest(
                prompt=prompt,
                seed=seed,
                guidance_scale=self.guidance_var.get(),
                num_images=self.num_images_var.get(),
                safety_tolerance=SafetyTolerance(self.safety_var.get()),
                output_format=OutputFormat(self.format_var.get()),
                aspect_ratio=AspectRatio(self.aspect_var.get())
            )

            # Update UI state
            self.generate_btn.config(state=tk.DISABLED)
            self.cancel_btn.config(state=tk.NORMAL)
            self.progress_bar.start(10)

            # Start generation in background thread
            self.current_generation_task = threading.Thread(
                target=self.run_generation,
                args=(request,),
                daemon=True
            )
            self.current_generation_task.start()

            self.log_message(f"Starting generation: '{prompt[:50]}...'")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start generation: {str(e)}")
            self.generation_complete()

    def run_generation(self, request: GenerationRequest):
        """Run the generation process in a background thread."""
        try:
            # Generate images
            result = self.flux_client.generate(request)

            # Download images
            if result.images:
                image_urls = [img.get('url') for img in result.images if img.get('url')]

                # Use asyncio to download images with proper loop handling
                loop = None
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    downloaded_images = loop.run_until_complete(
                        self.image_manager.download_images_batch(
                            image_urls,
                            request.prompt,
                            request.seed
                        )
                    )

                    # Wait for all pending tasks to complete
                    pending = asyncio.all_tasks(loop)
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

                finally:
                    if loop and not loop.is_closed():
                        loop.close()

                # Update UI on main thread
                self.after(0, lambda: self.generation_success(downloaded_images))
            else:
                self.after(0, lambda: self.generation_error("No images were generated"))

        except Exception as e:
            self.after(0, lambda: self.generation_error(str(e)))

    def generation_success(self, images):
        """Handle successful generation."""
        self.log_message(f"Generation completed! Downloaded {len(images)} images.")

        # Display images in preview
        if images:
            self.display_images(images)

            # Enable "Select from Gallery" button in editing tab
            self.select_generated_btn.config(state=tk.NORMAL)

        self.generation_complete()

    def display_images(self, images):
        """Display generated images in the preview section."""
        try:
            self.log_message(f"Attempting to display {len(images)} images")

            # Clear existing images
            self.clear_image_preview()

            # Hide placeholder
            if hasattr(self, 'preview_placeholder'):
                self.preview_placeholder.pack_forget()

            # Display each image
            valid_images = 0
            for i, image_info in enumerate(images):
                self.log_message(f"Processing image {i+1}: {type(image_info)}")

                if isinstance(image_info, Exception):
                    self.log_message(f"Error downloading image {i+1}: {str(image_info)}")
                    continue

                # Check if it's a valid ImageInfo object
                if hasattr(image_info, 'local_path') and image_info.local_path:
                    self.log_message(f"Adding image {i+1} to preview: {image_info.local_path}")
                    self.add_image_to_preview(image_info, i)
                    valid_images += 1
                else:
                    self.log_message(f"Image {i+1} has no local path or is invalid")

            if valid_images == 0:
                # Show a message if no valid images
                no_images_label = ttk.Label(
                    self.scrollable_frame,
                    text="No images could be displayed. Check the log for details.",
                    font=('Segoe UI', 10),
                    foreground=self.colors['text_muted']
                )
                no_images_label.pack(expand=True, pady=50)
                self.image_widgets.append(no_images_label)

            # Update scroll region
            self.scrollable_frame.update_idletasks()
            self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))

            self.log_message(f"Successfully displayed {valid_images} out of {len(images)} images")

        except Exception as e:
            self.log_message(f"Error displaying images: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def add_image_to_preview(self, image_info, index):
        """Add a single image to the preview section."""
        try:
            self.log_message(f"Creating preview for image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}")

            # Create frame for this image
            image_frame = ttk.Frame(self.scrollable_frame, style='Card.TFrame')
            image_frame.pack(fill=tk.X, padx=5, pady=5)

            # Get thumbnail for display
            self.log_message(f"Getting thumbnail for: {getattr(image_info, 'local_path', 'No path')}")
            thumbnail = self.image_manager.get_thumbnail_for_display(image_info, size=(200, 200))

            if thumbnail:
                self.log_message(f"Thumbnail created successfully for image {index + 1}")
                # Image display
                image_label = ttk.Label(image_frame, image=thumbnail)
                image_label.pack(side=tk.LEFT, padx=10, pady=10)

                # Keep reference to prevent garbage collection
                image_label.image = thumbnail

                # Info panel
                info_frame = ttk.Frame(image_frame)
                info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

                # Image info
                ttk.Label(info_frame, text=f"Image {index + 1}",
                         font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

                if hasattr(image_info, 'filename'):
                    ttk.Label(info_frame, text=f"File: {image_info.filename}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'width') and hasattr(image_info, 'height'):
                    ttk.Label(info_frame, text=f"Size: {image_info.width}x{image_info.height}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'seed') and image_info.seed:
                    ttk.Label(info_frame, text=f"Seed: {image_info.seed}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                # Buttons
                button_frame = ttk.Frame(info_frame)
                button_frame.pack(anchor=tk.W, pady=(10, 0))

                # Open button
                ttk.Button(button_frame, text="📂 Open",
                          command=lambda: self.open_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 5))

                # Export button
                ttk.Button(button_frame, text="💾 Export",
                          command=lambda: self.export_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 5))

                # Edit button
                ttk.Button(button_frame, text="✏️ Edit",
                          command=lambda: self.edit_generated_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT)

                # Store widget reference
                self.image_widgets.append(image_frame)

            else:
                # Fallback if thumbnail creation failed
                self.log_message(f"Thumbnail creation failed for image {index + 1}, showing text fallback")
                ttk.Label(image_frame, text=f"Image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}",
                         font=('Segoe UI', 10)).pack(padx=10, pady=10)
                self.image_widgets.append(image_frame)

        except Exception as e:
            self.log_message(f"Error adding image {index + 1} to preview: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def clear_image_preview(self):
        """Clear all images from the preview section."""
        for widget in self.image_widgets:
            widget.destroy()
        self.image_widgets.clear()

    def open_image(self, image_info):
        """Open an image in the default system viewer."""
        try:
            import os
            import subprocess
            import platform

            if hasattr(image_info, 'local_path') and image_info.local_path:
                if platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', image_info.local_path])
                elif platform.system() == 'Windows':  # Windows
                    os.startfile(image_info.local_path)
                else:  # Linux
                    subprocess.call(['xdg-open', image_info.local_path])

                self.log_message(f"Opened image: {image_info.filename}")
            else:
                messagebox.showerror("Error", "Image file not found")

        except Exception as e:
            self.log_message(f"Error opening image: {str(e)}")
            messagebox.showerror("Error", f"Could not open image: {str(e)}")

    def export_image(self, image_info):
        """Export an image to a chosen location."""
        try:
            if not hasattr(image_info, 'local_path') or not image_info.local_path:
                messagebox.showerror("Error", "Image file not found")
                return

            # Get export destination
            default_filename = getattr(image_info, 'filename', 'image.jpg')
            filename = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("JPEG files", "*.jpg"),
                    ("All files", "*.*")
                ],
                title="Export image as...",
                initialfile=default_filename
            )

            if filename:
                success = self.image_manager.export_image(image_info, filename)
                if success:
                    self.log_message(f"Image exported to: {filename}")
                    messagebox.showinfo("Success", f"Image exported successfully to:\n{filename}")
                else:
                    messagebox.showerror("Error", "Failed to export image")

        except Exception as e:
            self.log_message(f"Error exporting image: {str(e)}")
            messagebox.showerror("Error", f"Could not export image: {str(e)}")

    # Image Editing Methods
    def upload_image_file(self):
        """Handle image file upload for editing."""
        try:
            # Get supported file types
            file_types = self.image_manager.get_supported_image_formats()

            # Open file dialog
            file_path = filedialog.askopenfilename(
                title="Select image to edit",
                filetypes=file_types
            )

            if file_path:
                # Validate the image file
                if not self.image_manager.validate_image_file(file_path):
                    messagebox.showerror("Error", "Invalid image file. Please select a valid image.")
                    return

                self.log_message(f"Selected image for editing: {file_path}")
                self.uploaded_file_label.config(text=f"Selected: {Path(file_path).name}")

                # Upload to FAL storage
                self.upload_and_prepare_image(file_path)

        except Exception as e:
            self.log_message(f"Error selecting image file: {str(e)}")
            messagebox.showerror("Error", f"Failed to select image: {str(e)}")

    def upload_and_prepare_image(self, file_path):
        """Upload image to FAL storage and prepare for editing."""
        try:
            self.editing_status_label.config(text="Uploading image...")
            self.upload_file_btn.config(state=tk.DISABLED)

            # Start upload in background thread
            upload_thread = threading.Thread(
                target=self.run_image_upload,
                args=(file_path,),
                daemon=True
            )
            upload_thread.start()

        except Exception as e:
            self.log_message(f"Error starting image upload: {str(e)}")
            self.upload_file_btn.config(state=tk.NORMAL)

    def run_image_upload(self, file_path):
        """Run image upload in background thread."""
        try:
            # Upload image
            uploaded_url = self.flux_client.upload_image(file_path)

            # Create ImageInfo for uploaded image
            image_info = self.image_manager.create_uploaded_image_info(file_path, uploaded_url)

            # Update UI on main thread
            self.after(0, lambda: self.upload_success(image_info, uploaded_url))

        except Exception as e:
            self.after(0, lambda: self.upload_error(str(e)))
    
    def upload_error(self, error_message):
        """Handle upload errors."""
        self.editing_status_label.config(text="Upload failed!")
        self.log_message(f"Upload error: {error_message}")
        messagebox.showerror("Upload Error", f"Failed to upload image: {error_message}")
        
        # Reset edit button state
        self.edit_btn.config(state=tk.DISABLED)

    def upload_success(self, image_info, uploaded_url):
        """Handle successful image upload."""
        self.current_editing_image = image_info
        self.current_editing_image_url = uploaded_url

        # Update UI
        self.current_image_info.config(
            text=f"✅ Ready to edit: {image_info.filename} ({image_info.width}x{image_info.height})"
        )
        self.edit_btn.config(state=tk.NORMAL)
        self.upload_file_btn.config(state=tk.NORMAL)
        self.editing_status_label.config(text="Image uploaded successfully. Ready to edit!")

        self.log_message(f"Image uploaded and ready for editing: {image_info.filename}")

    def upload_error(self, error_message):
        """Handle image upload error."""
        self.upload_file_btn.config(state=tk.NORMAL)
        self.editing_status_label.config(text="Upload failed. Please try again.")
        self.log_message(f"Image upload failed: {error_message}")
        messagebox.showerror("Upload Error", f"Failed to upload image:\n{error_message}")

    def select_from_generated(self):
        """Allow user to select from previously generated images."""
        # Get recent images
        recent_images = self.image_manager.get_recent_images(20)

        if not recent_images:
            messagebox.showinfo("No Images", "No generated images available. Generate some images first!")
            return

        # Create selection dialog
        self.create_image_selection_dialog(recent_images)

    def create_image_selection_dialog(self, images):
        """Create a dialog to select from generated images."""
        dialog = tk.Toplevel(self)
        dialog.title("Select Image to Edit")
        dialog.geometry("600x400")
        dialog.transient(self)
        dialog.grab_set()

        # Center dialog
        dialog.geometry(f"+{self.winfo_x() + 100}+{self.winfo_y() + 100}")

        # Create scrollable frame
        canvas = tk.Canvas(dialog)
        scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Add images to selection
        for i, image_info in enumerate(images):
            if hasattr(image_info, 'local_path') and image_info.local_path:
                self.add_image_to_selection(scrollable_frame, image_info, dialog)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y", pady=10)

        # Add cancel button
        cancel_btn = ttk.Button(dialog, text="Cancel", command=dialog.destroy)
        cancel_btn.pack(side=tk.BOTTOM, pady=10)

    def add_image_to_selection(self, parent, image_info, dialog):
        """Add an image to the selection dialog."""
        try:
            frame = ttk.Frame(parent, style='Card.TFrame')
            frame.pack(fill=tk.X, padx=5, pady=5)

            # Get thumbnail
            thumbnail = self.image_manager.get_thumbnail_for_display(image_info, size=(100, 100))

            if thumbnail:
                # Image thumbnail
                img_label = ttk.Label(frame, image=thumbnail)
                img_label.pack(side=tk.LEFT, padx=10, pady=10)
                img_label.image = thumbnail  # Keep reference

                # Image info
                info_frame = ttk.Frame(frame)
                info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

                ttk.Label(info_frame, text=image_info.filename, font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)
                ttk.Label(info_frame, text=f"Size: {image_info.width}x{image_info.height}").pack(anchor=tk.W)
                if image_info.prompt:
                    ttk.Label(info_frame, text=f"Prompt: {image_info.prompt[:50]}...").pack(anchor=tk.W)

                # Select button
                select_btn = ttk.Button(
                    frame,
                    text="Select",
                    command=lambda: self.select_image_for_editing(image_info, dialog)
                )
                select_btn.pack(side=tk.RIGHT, padx=10, pady=10)

        except Exception as e:
            self.log_message(f"Error adding image to selection: {str(e)}")

    def select_image_for_editing(self, image_info, dialog):
        """Select an image for editing from the gallery."""
        try:
            # Upload the selected image to FAL storage
            if image_info.local_path:
                dialog.destroy()
                self.editing_status_label.config(text="Uploading selected image...")

                # Start upload in background
                upload_thread = threading.Thread(
                    target=self.run_selected_image_upload,
                    args=(image_info,),
                    daemon=True
                )
                upload_thread.start()

        except Exception as e:
            self.log_message(f"Error selecting image for editing: {str(e)}")
            messagebox.showerror("Error", f"Failed to select image: {str(e)}")

    def run_selected_image_upload(self, image_info):
        """Upload selected image in background thread."""
        try:
            # Upload the local image file
            uploaded_url = self.flux_client.upload_image(image_info.local_path)

            # Update UI on main thread
            self.after(0, lambda: self.selected_image_upload_success(image_info, uploaded_url))

        except Exception as e:
            self.after(0, lambda: self.upload_error(str(e)))

    def selected_image_upload_success(self, image_info, uploaded_url):
        """Handle successful upload of selected image."""
        self.current_editing_image = image_info
        self.current_editing_image_url = uploaded_url

        # Update UI
        self.current_image_info.config(
            text=f"✅ Ready to edit: {image_info.filename} ({image_info.width}x{image_info.height})"
        )
        self.edit_btn.config(state=tk.NORMAL)
        self.editing_status_label.config(text="Selected image ready for editing!")

        self.log_message(f"Selected image ready for editing: {image_info.filename}")

    def set_editing_prompt(self, prompt):
        """Set the editing prompt text."""
        self.editing_prompt_text.delete(1.0, tk.END)
        self.editing_prompt_text.insert(1.0, prompt)

    def randomize_editing_seed(self):
        """Generate a random seed for editing."""
        import random
        seed = random.randint(0, 2**32 - 1)
        self.editing_seed_var.set(str(seed))

    def update_editing_guidance_label(self, value):
        """Update the editing guidance scale label."""
        self.editing_guidance_label.config(text=f"{float(value):.1f}")

    def start_editing(self):
        """Start the image editing process."""
        # Validate inputs
        editing_prompt = self.editing_prompt_text.get(1.0, tk.END).strip()
        if not editing_prompt:
            messagebox.showwarning("Warning", "Please enter editing instructions.")
            return

        if not self.current_editing_image_url:
            messagebox.showerror("Error", "No image selected for editing.")
            return

        if not self.flux_client:
            messagebox.showerror("Error", "FLUX client not initialized. Please check your API key.")
            return

        # Prepare editing request
        try:
            from generator import EditingRequest

            seed_str = self.editing_seed_var.get().strip()
            seed = int(seed_str) if seed_str and seed_str.isdigit() else None

            request = EditingRequest(
                prompt=editing_prompt,
                image_url=self.current_editing_image_url,
                seed=seed,
                guidance_scale=self.editing_guidance_var.get(),
                num_images=self.editing_num_images_var.get(),
                safety_tolerance=SafetyTolerance(self.editing_safety_var.get()),
                output_format=OutputFormat(self.editing_format_var.get())
            )

            # Update UI state
            self.edit_btn.config(state=tk.DISABLED)
            self.cancel_edit_btn.config(state=tk.NORMAL)
            self.editing_progress_bar.start(10)

            # Start editing in background thread
            self.current_editing_task = threading.Thread(
                target=self.run_editing,
                args=(request,),
                daemon=True
            )
            self.current_editing_task.start()

            self.log_message(f"Starting image editing: '{editing_prompt[:50]}...'")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to start editing: {str(e)}")
            self.editing_complete()

    def run_editing(self, request):
        """Run the editing process in a background thread."""
        loop = None
        try:
            # Create a single event loop for the entire editing process
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the entire editing process in the same loop
            result = loop.run_until_complete(self.run_editing_async(request))
            
            # Wait for all pending tasks to complete
            pending = asyncio.all_tasks(loop)
            if pending:
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            
            # Update UI on main thread
            if result:
                self.after(0, lambda: self.editing_success(result))
            else:
                self.after(0, lambda: self.editing_error("No edited images were generated"))

        except Exception as e:
            self.after(0, lambda: self.editing_error(str(e)))
        finally:
            if loop and not loop.is_closed():
                loop.close()
    
    async def run_editing_async(self, request):
        """Run the editing process asynchronously."""
        try:
            # Edit image using async method
            result = await self.flux_client.edit_image_async(request)

            # Download edited images if available
            if result.images:
                image_urls = [img.get('url') for img in result.images if img.get('url')]
                
                downloaded_images = await self.image_manager.download_edited_images_batch(
                    image_urls,
                    request.prompt,
                    request.image_url,
                    request.seed
                )
                
                return downloaded_images
            else:
                return None
                
        except Exception as e:
            raise e

    def editing_success(self, images):
        """Handle successful editing."""
        self.log_message(f"Image editing completed! Downloaded {len(images)} edited images.")

        # Display edited images
        if images:
            self.display_edited_images(images)

            # Create editing history link
            if self.current_editing_image and len(images) > 0:
                for img in images:
                    if hasattr(img, 'local_path') and not isinstance(img, Exception):
                        self.image_manager.create_editing_history_entry(self.current_editing_image, img)

        self.editing_complete()

    def editing_error(self, error_message):
        """Handle editing error."""
        self.log_message(f"Image editing failed: {error_message}")
        messagebox.showerror("Editing Error", f"Failed to edit image:\n{error_message}")
        self.editing_complete()

    def editing_complete(self):
        """Reset UI after editing completion."""
        self.edit_btn.config(state=tk.NORMAL if self.current_editing_image_url else tk.DISABLED)
        self.cancel_edit_btn.config(state=tk.DISABLED)
        self.editing_progress_bar.stop()
        self.editing_status_label.config(text="Ready for next editing operation")
        self.current_editing_task = None

    def cancel_editing(self):
        """Cancel the current editing operation."""
        if self.current_editing_task and self.current_editing_task.is_alive():
            self.log_message("Image editing cancelled by user")
            # Note: Actual cancellation would require more complex thread management
            self.editing_complete()

    def display_edited_images(self, images):
        """Display edited images in the editing preview section."""
        try:
            self.log_message(f"Attempting to display {len(images)} edited images")

            # Clear existing images
            self.clear_editing_preview()

            # Hide placeholder
            if hasattr(self, 'editing_preview_placeholder'):
                self.editing_preview_placeholder.pack_forget()

            # Display each edited image
            valid_images = 0
            for i, image_info in enumerate(images):
                self.log_message(f"Processing edited image {i+1}: {type(image_info)}")

                if isinstance(image_info, Exception):
                    self.log_message(f"Error downloading edited image {i+1}: {str(image_info)}")
                    continue

                # Check if it's a valid ImageInfo object
                if hasattr(image_info, 'local_path') and image_info.local_path:
                    self.log_message(f"Adding edited image {i+1} to preview: {image_info.local_path}")
                    self.add_edited_image_to_preview(image_info, i)
                    valid_images += 1
                else:
                    self.log_message(f"Edited image {i+1} has no local path or is invalid")

            if valid_images == 0:
                # Show a message if no valid images
                no_images_label = ttk.Label(
                    self.editing_scrollable_frame,
                    text="No edited images could be displayed. Check the log for details.",
                    font=('Segoe UI', 10),
                    foreground=self.colors['text_muted']
                )
                no_images_label.pack(expand=True, pady=50)
                self.editing_image_widgets.append(no_images_label)

            # Update scroll region
            self.editing_scrollable_frame.update_idletasks()
            self.editing_preview_canvas.configure(scrollregion=self.editing_preview_canvas.bbox("all"))

            self.log_message(f"Successfully displayed {valid_images} out of {len(images)} edited images")

        except Exception as e:
            self.log_message(f"Error displaying edited images: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def add_edited_image_to_preview(self, image_info, index):
        """Add a single edited image to the preview section."""
        try:
            self.log_message(f"Creating preview for edited image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}")

            # Create frame for this edited image
            image_frame = ttk.Frame(self.editing_scrollable_frame, style='Card.TFrame')
            image_frame.pack(fill=tk.X, padx=5, pady=5)

            # Get thumbnail for display
            self.log_message(f"Getting thumbnail for edited image: {getattr(image_info, 'local_path', 'No path')}")
            thumbnail = self.image_manager.get_thumbnail_for_display(image_info, size=(200, 200))

            if thumbnail:
                self.log_message(f"Thumbnail created successfully for edited image {index + 1}")

                # Image display
                image_label = ttk.Label(image_frame, image=thumbnail)
                image_label.pack(side=tk.LEFT, padx=10, pady=10)

                # Keep reference to prevent garbage collection
                image_label.image = thumbnail

                # Info panel
                info_frame = ttk.Frame(image_frame)
                info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)

                # Image info
                ttk.Label(info_frame, text=f"Edited Image {index + 1}",
                         font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

                if hasattr(image_info, 'filename'):
                    ttk.Label(info_frame, text=f"File: {image_info.filename}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'width') and hasattr(image_info, 'height'):
                    ttk.Label(info_frame, text=f"Size: {image_info.width}x{image_info.height}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'editing_prompt') and image_info.editing_prompt:
                    ttk.Label(info_frame, text=f"Edit: {image_info.editing_prompt[:30]}...",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                if hasattr(image_info, 'seed') and image_info.seed:
                    ttk.Label(info_frame, text=f"Seed: {image_info.seed}",
                             font=('Segoe UI', 9)).pack(anchor=tk.W)

                # Buttons
                button_frame = ttk.Frame(info_frame)
                button_frame.pack(anchor=tk.W, pady=(10, 0))

                # Open button
                ttk.Button(button_frame, text="📂 Open",
                          command=lambda: self.open_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 5))

                # Export button
                ttk.Button(button_frame, text="💾 Export",
                          command=lambda: self.export_image(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT, padx=(0, 5))

                # Edit again button
                ttk.Button(button_frame, text="✏️ Edit Again",
                          command=lambda: self.edit_again(image_info),
                          style='Modern.TButton').pack(side=tk.LEFT)

                # Store widget reference
                self.editing_image_widgets.append(image_frame)

            else:
                # Fallback if thumbnail creation failed
                self.log_message(f"Thumbnail creation failed for edited image {index + 1}, showing text fallback")
                ttk.Label(image_frame, text=f"Edited Image {index + 1}: {getattr(image_info, 'filename', 'Unknown')}",
                         font=('Segoe UI', 10)).pack(padx=10, pady=10)
                self.editing_image_widgets.append(image_frame)

        except Exception as e:
            self.log_message(f"Error adding edited image {index + 1} to preview: {str(e)}")
            import traceback
            self.log_message(f"Traceback: {traceback.format_exc()}")

    def clear_editing_preview(self):
        """Clear all edited images from the preview section."""
        for widget in self.editing_image_widgets:
            widget.destroy()
        self.editing_image_widgets.clear()

    def edit_again(self, image_info):
        """Use an edited image as the source for another edit."""
        try:
            if hasattr(image_info, 'local_path') and image_info.local_path:
                # Switch to editing tab if not already there
                self.notebook.select(self.editing_tab)

                # Upload this image for editing
                self.editing_status_label.config(text="Preparing image for re-editing...")

                upload_thread = threading.Thread(
                    target=self.run_selected_image_upload,
                    args=(image_info,),
                    daemon=True
                )
                upload_thread.start()

                self.log_message(f"Preparing edited image for re-editing: {image_info.filename}")
            else:
                messagebox.showerror("Error", "Image file not available for re-editing")

        except Exception as e:
            self.log_message(f"Error preparing image for re-editing: {str(e)}")
            messagebox.showerror("Error", f"Failed to prepare image for re-editing: {str(e)}")

    def edit_generated_image(self, image_info):
        """Edit a generated image by switching to editing tab and preparing it."""
        try:
            # Switch to editing tab
            self.notebook.select(self.editing_tab)

            # Prepare the image for editing
            self.editing_status_label.config(text="Preparing generated image for editing...")

            upload_thread = threading.Thread(
                target=self.run_selected_image_upload,
                args=(image_info,),
                daemon=True
            )
            upload_thread.start()

            self.log_message(f"Preparing generated image for editing: {image_info.filename}")

        except Exception as e:
            self.log_message(f"Error preparing generated image for editing: {str(e)}")
            messagebox.showerror("Error", f"Failed to prepare image for editing: {str(e)}")

    def generation_error(self, error_message: str):
        """Handle generation error."""
        self.log_message(f"Generation failed: {error_message}")
        messagebox.showerror("Generation Error", f"Failed to generate images:\n{error_message}")
        self.generation_complete()

    def generation_complete(self):
        """Reset UI after generation completion."""
        self.generate_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.update_progress("Ready to generate images")
        self.current_generation_task = None

    def cancel_generation(self):
        """Cancel the current generation."""
        if self.current_generation_task and self.current_generation_task.is_alive():
            self.log_message("Generation cancelled by user")
            # Note: Actual cancellation would require more complex thread management
            self.generation_complete()

    def open_settings(self):
        """Open the settings dialog."""
        SettingsDialog(self, self.config_manager)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for common actions."""
        # Bind keyboard shortcuts
        self.bind_all('<Control-g>', lambda e: self.trigger_generation())
        self.bind_all('<Control-o>', lambda e: self.upload_image_file())
        self.bind_all('<Control-s>', lambda e: self.open_settings())
        self.bind_all('<Control-q>', lambda e: self.quit())
        self.bind_all('<F5>', lambda e: self.trigger_generation())
        self.bind_all('<Control-1>', lambda e: self.notebook.select(0))  # Switch to generation tab
        self.bind_all('<Control-2>', lambda e: self.notebook.select(1))  # Switch to editing tab
        self.bind_all('<Control-r>', lambda e: self.randomize_seed())
        self.bind_all('<Escape>', lambda e: self.cancel_generation())
        
        # Add tooltip information about shortcuts
        self.create_shortcuts_help()
    
    def create_shortcuts_help(self):
        """Create a help tooltip showing keyboard shortcuts."""
        shortcuts_text = (
            "Keyboard Shortcuts:\n"
            "Ctrl+G or F5: Generate Images\n"
            "Ctrl+O: Upload Image for Editing\n"
            "Ctrl+S: Open Settings\n"
            "Ctrl+1: Switch to Generation Tab\n"
            "Ctrl+2: Switch to Editing Tab\n"
            "Ctrl+R: Randomize Seed\n"
            "Escape: Cancel Generation\n"
            "Ctrl+Q: Quit Application"
        )
        
        # Store shortcuts text for later access
        self.shortcuts_text = shortcuts_text
    
    def trigger_generation(self):
        """Trigger image generation via keyboard shortcut."""
        if self.notebook.index(self.notebook.select()) == 0:  # Generation tab
            if hasattr(self, 'generate_btn') and self.generate_btn['state'] == tk.NORMAL:
                self.generate_images()
        else:  # Editing tab
            if hasattr(self, 'edit_btn') and self.edit_btn['state'] == tk.NORMAL:
                self.edit_image()
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality for the main window."""
        if DRAG_DROP_AVAILABLE:
            # Enable drag and drop events using tkinterdnd2
            self.drop_target_register(DND_FILES)
            self.dnd_bind('<<Drop>>', self.handle_drop)
            self.dnd_bind('<<DragEnter>>', self.handle_drag_enter)
            self.dnd_bind('<<DragLeave>>', self.handle_drag_leave)
        else:
            # Fallback: disable drag and drop functionality
            pass
    
    def enable_drag_drop_for_upload(self, widget):
        """Enable drag and drop for a specific widget (image upload area)."""
        try:
            if DRAG_DROP_AVAILABLE:
                # Enable drag and drop for this specific widget
                widget.drop_target_register(DND_FILES)
                widget.dnd_bind('<<Drop>>', self.handle_widget_drop)
                
                # Visual feedback for drag and drop
                widget.bind('<Enter>', lambda e: self.show_drop_hint(widget))
                widget.bind('<Leave>', lambda e: self.hide_drop_hint(widget))
                
                # Create a drop zone label
                drop_label = ttk.Label(
                    widget,
                    text="💾 Drag and drop images here or click 'Choose Image File'",
                    font=('Segoe UI', 9, 'italic'),
                    foreground=self.colors['text_muted']
                )
                drop_label.pack(pady=10)
            else:
                # Create a label indicating drag and drop is not available
                drop_label = ttk.Label(
                    widget,
                    text="📁 Click 'Choose Image File' to select an image",
                    font=('Segoe UI', 9, 'italic'),
                    foreground=self.colors['text_muted']
                )
                drop_label.pack(pady=10)
            
        except Exception as e:
            # Fallback if drag and drop setup fails
            self.log_message(f"Drag and drop setup failed: {e}")
            # Create a fallback label
            drop_label = ttk.Label(
                widget,
                text="📁 Click 'Choose Image File' to select an image",
                font=('Segoe UI', 9, 'italic'),
                foreground=self.colors['text_muted']
            )
            drop_label.pack(pady=10)
    
    def handle_drop(self, event):
        """Handle file drop events on the main window."""
        try:
            if DRAG_DROP_AVAILABLE:
                # Get dropped files from tkinterdnd2
                files = self.tk.splitlist(event.data)
            else:
                # Fallback for manual testing
                files = event.data.split() if hasattr(event, 'data') else []
                
            if files:
                file_path = files[0].strip('{}')  # Remove braces if present
                
                # Check if it's an image file
                valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
                if Path(file_path).suffix.lower() in valid_extensions:
                    # Switch to editing tab and load the image
                    self.notebook.select(1)
                    self.load_dropped_image(file_path)
                else:
                    messagebox.showerror("Invalid File", "Please drop a valid image file (JPG, PNG, BMP, TIFF, WebP)")
        except Exception as e:
            self.log_message(f"Error handling dropped file: {e}")
            messagebox.showerror("Drop Error", f"Failed to process dropped file: {e}")
    
    def handle_widget_drop(self, event):
        """Handle file drop events on specific widgets (upload areas)."""
        try:
            if DRAG_DROP_AVAILABLE:
                # Get dropped files from tkinterdnd2
                files = self.tk.splitlist(event.data)
            else:
                # Fallback for manual testing
                files = event.data.split() if hasattr(event, 'data') else []
                
            if files:
                file_path = files[0].strip('{}')  # Remove braces if present
                
                # Check if it's an image file
                valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
                if Path(file_path).suffix.lower() in valid_extensions:
                    # Load the image for editing
                    self.load_dropped_image(file_path)
                else:
                    messagebox.showerror("Invalid File", "Please drop a valid image file (JPG, PNG, BMP, TIFF, WebP)")
        except Exception as e:
            self.log_message(f"Error handling dropped file: {e}")
            messagebox.showerror("Drop Error", f"Failed to process dropped file: {e}")
    
    def handle_drag_enter(self, event):
        """Handle drag enter events for visual feedback."""
        self.configure(bg='#e3f2fd')  # Light blue background
    
    def handle_drag_leave(self, event):
        """Handle drag leave events."""
        self.configure(bg=self.colors['bg'])  # Restore original background
    
    def load_dropped_image(self, file_path):
        """Load a dropped image file for editing."""
        try:
            # Update the file label
            self.uploaded_file_label.config(text=Path(file_path).name)
            
            # Store the image path
            self.current_editing_image = file_path
            self.current_editing_image_url = None
            
            # Update current image info
            self.current_image_info.config(
                text=f"Selected: {Path(file_path).name}"
            )
            
            self.log_message(f"Loaded image for editing: {Path(file_path).name}")
            
            # Auto-upload the dropped image like gallery selection does
            if self.flux_client:
                self.editing_status_label.config(text="Uploading dropped image...")
                
                # Start upload in background thread
                upload_thread = threading.Thread(
                    target=self.run_dropped_image_upload,
                    args=(file_path,),
                    daemon=True
                )
                upload_thread.start()
            else:
                self.log_message("FLUX client not available - cannot upload image")
                messagebox.showwarning("Warning", "FLUX client not initialized. Please check your API key.")
            
        except Exception as e:
            self.log_message(f"Error loading dropped image: {e}")
            messagebox.showerror("Load Error", f"Failed to load image: {e}")
    
    def run_dropped_image_upload(self, file_path):
        """Upload dropped image in background thread."""
        try:
            # Upload the local image file
            uploaded_url = self.flux_client.upload_image(file_path)
            
            # Update UI on main thread
            self.after(0, lambda: self.dropped_image_upload_success(file_path, uploaded_url))
            
        except Exception as e:
            self.after(0, lambda: self.upload_error(str(e)))
    
    def dropped_image_upload_success(self, file_path, uploaded_url):
        """Handle successful upload of dropped image."""
        self.current_editing_image_url = uploaded_url
        
        # Update UI
        self.current_image_info.config(
            text=f"✅ Ready to edit: {Path(file_path).name}"
        )
        self.edit_btn.config(state=tk.NORMAL)
        self.editing_status_label.config(text="Dropped image ready for editing!")
        
        self.log_message(f"Dropped image uploaded and ready for editing: {Path(file_path).name}")
    
    def show_drop_hint(self, widget):
        """Show visual hint for drag and drop."""
        widget.configure(relief='ridge', borderwidth=2)
    
    def hide_drop_hint(self, widget):
        """Hide visual hint for drag and drop."""
        widget.configure(relief='flat', borderwidth=1)
    
    # Fallback methods for systems without tkinterdnd2
    def drop_target_register(self, *args):
        """Fallback method for drag and drop registration."""
        if not DRAG_DROP_AVAILABLE:
            pass
    
    def dnd_bind(self, *args):
        """Fallback method for drag and drop binding."""
        if not DRAG_DROP_AVAILABLE:
            pass
    
    def randomize_seed(self):
        """Randomize the seed value for image generation."""
        try:
            import random
            new_seed = random.randint(1, 2147483647)
            if hasattr(self, 'seed_var'):
                self.seed_var.set(str(new_seed))
                self.log_message(f"Seed randomized to: {new_seed}")
        except Exception as e:
            self.log_message(f"Error randomizing seed: {e}")
    
    def cancel_generation(self):
        """Cancel ongoing image generation."""
        try:
            # Set cancellation flag if it exists
            if hasattr(self, 'generation_cancelled'):
                self.generation_cancelled = True
                self.log_message("Generation cancelled by user")
                
            # Re-enable generate button if it exists
            if hasattr(self, 'generate_btn'):
                self.generate_btn.config(state=tk.NORMAL, text="Generate Images")
                
            if hasattr(self, 'edit_btn'):
                self.edit_btn.config(state=tk.NORMAL, text="Edit Image")
                
        except Exception as e:
            self.log_message(f"Error cancelling generation: {e}")
    
    def upload_image_file(self):
        """Open file dialog to upload an image for editing."""
        try:
            # Switch to editing tab first
            self.notebook.select(1)
            
            # Open file dialog
            file_path = filedialog.askopenfilename(
                title="Select Image for Editing",
                filetypes=[
                    ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.webp"),
                    ("All files", "*.*")
                ]
            )
            
            if file_path:
                # Use the same upload workflow as drag-and-drop
                self.load_dropped_image(file_path)
                
        except Exception as e:
             self.log_message(f"Error uploading image file: {e}")
             messagebox.showerror("Upload Error", f"Failed to upload image: {e}")
    
    def show_shortcuts_help(self):
        """Show the keyboard shortcuts help dialog."""
        if hasattr(self, 'shortcuts_text'):
            messagebox.showinfo("Keyboard Shortcuts", self.shortcuts_text)
        else:
            # Fallback if shortcuts_text is not available
            shortcuts_text = (
                "Keyboard Shortcuts:\n"
                "Ctrl+G or F5: Generate Images\n"
                "Ctrl+O: Upload Image for Editing\n"
                "Ctrl+S: Open Settings\n"
                "Ctrl+1: Switch to Generation Tab\n"
                "Ctrl+2: Switch to Editing Tab\n"
                "Ctrl+R: Randomize Seed\n"
                "Escape: Cancel Generation\n"
                "Ctrl+Q: Quit Application"
            )
            messagebox.showinfo("Keyboard Shortcuts", shortcuts_text)


class SettingsDialog(tk.Toplevel):
    """Settings dialog for configuring the application."""

    def __init__(self, parent, config_manager: ConfigManager):
        super().__init__(parent)
        self.parent = parent
        self.config_manager = config_manager
        self.config = config_manager.get_config()

        self.setup_dialog()
        self.create_widgets()
        self.load_current_settings()

    def setup_dialog(self):
        """Setup the dialog window."""
        self.title("Settings")
        self.geometry("500x400")
        self.resizable(False, False)
        self.transient(self.parent)
        self.grab_set()

        # Center on parent
        self.geometry(f"+{self.parent.winfo_x() + 50}+{self.parent.winfo_y() + 50}")

    def create_widgets(self):
        """Create settings widgets."""
        # Main frame
        main_frame = ttk.Frame(self, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # API Key section
        api_frame = ttk.LabelFrame(main_frame, text="API Configuration", padding=15)
        api_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(api_frame, text="FAL API Key:").pack(anchor=tk.W)
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=50)
        api_entry.pack(fill=tk.X, pady=(5, 0))

        # Output settings
        output_frame = ttk.LabelFrame(main_frame, text="Output Settings", padding=15)
        output_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(output_frame, text="Output Directory:").pack(anchor=tk.W)
        dir_frame = ttk.Frame(output_frame)
        dir_frame.pack(fill=tk.X, pady=(5, 10))

        self.output_dir_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.output_dir_var, width=40).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(dir_frame, text="Browse", command=self.browse_directory).pack(side=tk.RIGHT, padx=(5, 0))

        # Auto-open images checkbox
        self.auto_open_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="Auto-open generated images", variable=self.auto_open_var).pack(anchor=tk.W)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Button(button_frame, text="Cancel", command=self.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Save", command=self.save_settings).pack(side=tk.RIGHT, padx=(0, 10))

    def load_current_settings(self):
        """Load current settings into the dialog."""
        self.api_key_var.set(self.config.api_key)
        self.output_dir_var.set(self.config.output_directory)
        self.auto_open_var.set(self.config.auto_open_images)

    def browse_directory(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)

    def save_settings(self):
        """Save the settings."""
        try:
            # Update configuration
            self.config_manager.update_config(
                api_key=self.api_key_var.get(),
                output_directory=self.output_dir_var.get(),
                auto_open_images=self.auto_open_var.get()
            )

            # Reinitialize parent's client
            self.parent.initialize_client()

            messagebox.showinfo("Settings", "Settings saved successfully!")
            self.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")


if __name__ == "__main__":
    app = ModernFluxGUI()
    app.mainloop()
