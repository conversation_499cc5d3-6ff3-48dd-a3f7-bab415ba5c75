#!/usr/bin/env python3
"""
Test script to verify network error handling and retry logic.
"""

import asyncio
import logging
from pathlib import Path
from image_manager import ImageManager

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_dns_error_handling():
    """Test DNS error handling with a non-existent domain."""
    print("=" * 80)
    print("TESTING DNS ERROR HANDLING")
    print("=" * 80)
    
    # Create image manager
    output_dir = Path("./output")
    image_manager = ImageManager(output_dir)
    
    # Test with a non-existent domain that will cause DNS errors
    fake_url = "https://non-existent-domain-12345.com/fake-image.png"
    
    try:
        print(f"🔗 Testing DNS error handling with: {fake_url}")
        print("⏳ This should fail with DNS error and retry 3 times...")
        
        # This should fail with DNS error
        result = await image_manager.download_edited_image(
            url=fake_url,
            editing_prompt="Test prompt",
            original_image_url="test_original.png",
            seed=12345
        )
        
        print("❌ Unexpected success - this should have failed!")
        return False
        
    except Exception as e:
        error_msg = str(e)
        print(f"✅ Expected error caught: {error_msg}")
        
        # Check if the error message contains our user-friendly DNS message
        if "DNS resolution failed" in error_msg:
            print("✅ DNS error properly detected and handled")
            return True
        elif "Failed to download edited image after" in error_msg and "attempts" in error_msg:
            print("✅ Retry logic working - failed after multiple attempts")
            return True
        else:
            print(f"❌ Unexpected error format: {error_msg}")
            return False

async def test_timeout_handling():
    """Test timeout handling with a slow endpoint."""
    print("\n" + "=" * 80)
    print("TESTING TIMEOUT HANDLING")
    print("=" * 80)
    
    # Create image manager
    output_dir = Path("./output")
    image_manager = ImageManager(output_dir)
    
    # Test with httpbin delay endpoint (10 second delay)
    slow_url = "https://httpbin.org/delay/10"
    
    try:
        print(f"🔗 Testing timeout handling with: {slow_url}")
        print("⏳ This should timeout and retry...")
        
        # This should timeout (our timeout is set to 60 seconds total, 30 seconds connect)
        # But httpbin delay might not work as expected for this test
        result = await image_manager.download_edited_image(
            url=slow_url,
            editing_prompt="Test prompt",
            original_image_url="test_original.png",
            seed=12345
        )
        
        print("❌ Unexpected success - this should have failed or taken a long time!")
        return False
        
    except Exception as e:
        error_msg = str(e)
        print(f"✅ Expected error caught: {error_msg}")
        
        # Check if timeout was handled properly
        if "Request timed out" in error_msg or "timeout" in error_msg.lower():
            print("✅ Timeout error properly detected and handled")
            return True
        elif "Failed to download edited image after" in error_msg and "attempts" in error_msg:
            print("✅ Retry logic working - failed after multiple attempts")
            return True
        else:
            print(f"⚠️ Different error than expected: {error_msg}")
            return True  # Still counts as handled

async def test_successful_download():
    """Test successful download with a real endpoint."""
    print("\n" + "=" * 80)
    print("TESTING SUCCESSFUL DOWNLOAD")
    print("=" * 80)
    
    # Create image manager
    output_dir = Path("./output")
    image_manager = ImageManager(output_dir)
    
    # Test with a real image URL (small test image)
    test_url = "https://httpbin.org/image/png"
    
    try:
        print(f"🔗 Testing successful download with: {test_url}")
        print("⏳ This should succeed...")
        
        result = await image_manager.download_edited_image(
            url=test_url,
            editing_prompt="Test prompt",
            original_image_url="test_original.png",
            seed=12345
        )
        
        if result and result.local_path:
            print(f"✅ Download successful: {result.filename}")
            print(f"✅ File saved to: {result.local_path}")
            return True
        else:
            print("❌ Download failed - no result returned")
            return False
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Unexpected error: {error_msg}")
        return False

async def main():
    """Run all network error handling tests."""
    print("🧪 NETWORK ERROR HANDLING TEST SUITE")
    print("=" * 80)
    
    results = []
    
    # Test DNS error handling
    dns_result = await test_dns_error_handling()
    results.append(("DNS Error Handling", dns_result))
    
    # Test timeout handling (might be flaky depending on network)
    timeout_result = await test_timeout_handling()
    results.append(("Timeout Handling", timeout_result))
    
    # Test successful download
    success_result = await test_successful_download()
    results.append(("Successful Download", success_result))
    
    # Print summary
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All network error handling tests passed!")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Check the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
