"""
Test Script for FLUX.1 Kontext Pro Image Generator
Validates core functionality without requiring GUI interaction.
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import all modules at module level
from generator import FluxKontextClient, GenerationRequest, SafetyTolerance, OutputFormat, AspectRatio
from settings import ConfigManager, FluxKontextConfig
from image_manager import ImageManager, ImageInfo
from logger import setup_logging, FluxLogger, ErrorHandler, ErrorType

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing imports...")
    
    try:
        from generator import FluxKontextClient, GenerationRequest, SafetyTolerance, OutputFormat, AspectRatio
        print("✅ Generator module imported successfully")
        
        from settings import ConfigManager, FluxKontextConfig
        print("✅ Settings module imported successfully")
        
        from image_manager import ImageManager, ImageInfo
        print("✅ Image manager module imported successfully")
        
        from logger import setup_logging, FluxLogger, ErrorHandler, ErrorType
        print("✅ Logger module imported successfully")
        
        # Test GUI import (may fail if tkinter not available)
        try:
            from modern_gui import ModernFluxGUI
            print("✅ Modern GUI module imported successfully")
        except ImportError as e:
            print(f"⚠️  GUI module import failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def test_configuration():
    """Test configuration management."""
    print("\nTesting configuration...")
    
    try:
        # Test config creation
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        print(f"✅ Configuration loaded: {type(config).__name__}")
        print(f"   - Output directory: {config.output_directory}")
        print(f"   - Model endpoint: {config.model_endpoint}")
        print(f"   - Default guidance scale: {config.guidance_scale}")
        
        # Test config validation
        config.validate()
        print("✅ Configuration validation passed")
        
        # Test parameter updates
        config_manager.update_config(guidance_scale=5.0, num_images=2)
        updated_config = config_manager.get_config()
        
        if updated_config.guidance_scale == 5.0 and updated_config.num_images == 2:
            print("✅ Configuration updates working")
        else:
            print("❌ Configuration updates failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_generation_request():
    """Test generation request creation and validation."""
    print("\nTesting generation request...")
    
    try:
        # Test basic request creation
        request = GenerationRequest(
            prompt="A beautiful sunset over mountains",
            seed=12345,
            guidance_scale=3.5,
            num_images=1,
            safety_tolerance=SafetyTolerance.STRICT,
            output_format=OutputFormat.JPEG,
            aspect_ratio=AspectRatio.SQUARE
        )
        
        print("✅ Generation request created successfully")
        print(f"   - Prompt: {request.prompt[:30]}...")
        print(f"   - Seed: {request.seed}")
        print(f"   - Guidance scale: {request.guidance_scale}")
        print(f"   - Safety tolerance: {request.safety_tolerance.value}")
        print(f"   - Output format: {request.output_format.value}")
        print(f"   - Aspect ratio: {request.aspect_ratio.value}")
        
        # Test FluxKontextClient creation (without API key)
        try:
            client = FluxKontextClient(api_key="test_key")
            print("✅ FLUX client created successfully")
        except Exception as e:
            print(f"⚠️  FLUX client creation failed (expected without valid API key): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Generation request test failed: {e}")
        return False


def test_image_manager():
    """Test image manager functionality."""
    print("\nTesting image manager...")
    
    try:
        # Create image manager
        image_manager = ImageManager(output_dir="test_output")
        print("✅ Image manager created successfully")
        
        # Test directory creation
        if Path("test_output").exists():
            print("✅ Output directory created")
        
        # Test ImageInfo creation
        image_info = ImageInfo(
            url="https://example.com/test.jpg",
            filename="test_image.jpg",
            prompt="Test prompt",
            seed=12345,
            timestamp="20240101_120000"
        )
        
        print("✅ ImageInfo object created successfully")
        print(f"   - URL: {image_info.url}")
        print(f"   - Filename: {image_info.filename}")
        print(f"   - Prompt: {image_info.prompt}")
        
        # Cleanup test directory
        import shutil
        if Path("test_output").exists():
            shutil.rmtree("test_output")
            print("✅ Test cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Image manager test failed: {e}")
        return False


def test_logging():
    """Test logging functionality."""
    print("\nTesting logging...")
    
    try:
        # Setup logger
        logger = setup_logging(log_file="test_flux.log", console_output=False)
        print("✅ Logger setup successful")
        
        # Test different log levels
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.error("Test error message", error_type=ErrorType.API_ERROR)
        
        print("✅ Logging messages sent successfully")
        
        # Check if log file was created
        if Path("test_flux.log").exists():
            print("✅ Log file created successfully")
            
            # Read and display log content
            with open("test_flux.log", 'r') as f:
                log_content = f.read()
                if "Test info message" in log_content:
                    print("✅ Log content verification passed")
                else:
                    print("❌ Log content verification failed")
                    return False
            
            # Cleanup
            Path("test_flux.log").unlink()
            print("✅ Log test cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        return False


def test_enum_values():
    """Test enum value accessibility."""
    print("\nTesting enum values...")
    
    try:
        # Test SafetyTolerance enum
        safety_values = [e.value for e in SafetyTolerance]
        print(f"✅ SafetyTolerance values: {safety_values}")
        
        # Test OutputFormat enum
        format_values = [e.value for e in OutputFormat]
        print(f"✅ OutputFormat values: {format_values}")
        
        # Test AspectRatio enum
        aspect_values = [e.value for e in AspectRatio]
        print(f"✅ AspectRatio values: {aspect_values}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enum values test failed: {e}")
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("=" * 60)
    print("FLUX.1 Kontext Pro Image Generator - Test Suite")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Configuration Tests", test_configuration),
        ("Generation Request Tests", test_generation_request),
        ("Image Manager Tests", test_image_manager),
        ("Logging Tests", test_logging),
        ("Enum Values Tests", test_enum_values)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'=' * 40}")
        print(f"Running {test_name}")
        print(f"{'=' * 40}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'=' * 60}")
    print("TEST RESULTS SUMMARY")
    print(f"{'=' * 60}")
    print(f"Total tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success rate: {(passed / (passed + failed) * 100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! The application is ready to use.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the issues above.")
    
    return failed == 0


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
