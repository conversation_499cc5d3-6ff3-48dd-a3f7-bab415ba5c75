#!/usr/bin/env python3
"""
Test script to verify the keyboard shortcuts and drag-and-drop modifications
work correctly without running the full GUI application.
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported."""
    try:
        from modern_gui import ModernFluxGUI
        print("✅ ModernFluxGUI imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during import: {e}")
        return False

def test_class_methods():
    """Test that the new methods exist in the ModernFluxGUI class."""
    try:
        from modern_gui import ModernFluxGUI
        
        # Check if new methods exist
        required_methods = [
            'setup_keyboard_shortcuts',
            'setup_drag_drop',
            'trigger_generation',
            'randomize_seed',
            'cancel_generation',
            'upload_image_file',
            'show_shortcuts_help',
            'handle_drop',
            'load_dropped_image',
            'enable_drag_drop_for_upload'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(ModernFluxGUI, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ Missing methods: {missing_methods}")
            return False
        else:
            print("✅ All required methods are present")
            return True
            
    except Exception as e:
        print(f"❌ Error checking methods: {e}")
        return False

def test_syntax():
    """Test that the Python syntax is valid by compiling the file."""
    try:
        with open('modern_gui.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, 'modern_gui.py', 'exec')
        print("✅ Python syntax is valid")
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing keyboard shortcuts and drag-and-drop modifications...\n")
    
    tests = [
        ("Syntax Check", test_syntax),
        ("Import Test", test_imports),
        ("Method Check", test_class_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The modifications appear to be working correctly.")
        print("\nNew Features Added:")
        print("1. ⌨️  Keyboard Shortcuts:")
        print("   - Ctrl+G or F5: Generate Images")
        print("   - Ctrl+O: Upload Image for Editing")
        print("   - Ctrl+S: Open Settings")
        print("   - Ctrl+1/2: Switch between tabs")
        print("   - Ctrl+R: Randomize Seed")
        print("   - Escape: Cancel Generation")
        print("   - Ctrl+Q: Quit Application")
        print("\n2. 🖱️  Drag and Drop:")
        print("   - Drag image files directly onto the upload area")
        print("   - Visual feedback when dragging files")
        print("   - Automatic tab switching to editing mode")
        print("   - Support for common image formats")
        print("\n3. 🆘 Help Button:")
        print("   - Added shortcuts help button in the header")
        print("   - Shows all available keyboard shortcuts")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())