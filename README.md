# FLUX.1 Kontext Pro Image Generator

A comprehensive, modern GUI application for generating images using the FLUX.1 Kontext Pro API. This application provides a complete rewrite with improved architecture, user-friendly interface, and robust error handling.

## Features

### 🎨 Core Functionality
- **FLUX.1 Kontext Pro API Integration**: Full support for the latest FLUX.1 Kontext Pro text-to-image API
- **Image Editing with FLUX.1 Kontext Pro**: Advanced image editing capabilities using natural language prompts
- **Comprehensive Parameter Control**: All API parameters supported including:
  - Prompt input with quick prompt suggestions
  - Seed control with randomization
  - Guidance scale (0.0 - 20.0)
  - Number of images (1-10)
  - Safety tolerance levels (1-6)
  - Output formats (JPEG, PNG)
  - Aspect ratios (21:9, 16:9, 4:3, 3:2, 1:1, 2:3, 3:4, 9:16, 9:21)
- **Batch Image Generation**: Generate multiple images in a single request

### ✏️ Image Editing Features
- **Post-Generation Editing**: Edit generated images directly within the application
- **Image Upload for Editing**: Upload existing images from your computer to edit them
- **Natural Language Editing**: Use simple text prompts to describe desired changes
- **Editing History**: Track relationships between original and edited images
- **Multiple Editing Iterations**: Edit images multiple times with different prompts
- **Seamless Workflow**: Switch between generation and editing modes effortlessly

### 🖥️ Modern GUI Interface
- **Responsive Design**: Clean, modern interface that works on different screen sizes
- **Real-time Progress Monitoring**: Live progress updates and status indicators
- **Image Preview**: Built-in image preview and management
- **Comprehensive Logging**: Detailed generation logs with timestamps
- **Settings Management**: Easy-to-use settings dialog for configuration

### 🔧 Advanced Features
- **Image Management System**: Automatic image downloading, caching, and organization
- **Thumbnail Generation**: Automatic thumbnail creation for quick preview
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Configuration Management**: Robust configuration system with validation
- **Logging System**: Advanced logging with multiple output targets

## Installation

### Prerequisites
- Python 3.8 or higher
- FAL API key (get one from [fal.ai](https://fal.ai))

### Setup
1. Clone or download the application files
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

### Dependencies
- `fal-client>=0.4.0` - FLUX API client
- `aiohttp>=3.8.0` - Async HTTP client for image downloads
- `Pillow>=10.0.0` - Image processing and thumbnails

## Usage

### First Time Setup
1. Launch the application: `python main.py`
2. Click the "⚙️ Settings" button
3. Enter your FAL API key
4. Configure output directory and other preferences
5. Click "Save"

### Generating Images
1. Stay on the "🎨 Generate Images" tab (default)
2. Enter your image prompt in the text area
3. Adjust parameters as needed:
   - **Seed**: Leave empty for random, or enter a specific number for reproducible results
   - **Guidance Scale**: Higher values follow the prompt more closely (default: 3.5)
   - **Number of Images**: How many images to generate (1-10)
   - **Safety Tolerance**: Content filtering level (1=strict, 6=permissive)
   - **Output Format**: JPEG or PNG
   - **Aspect Ratio**: Choose from various aspect ratios
4. Click "🚀 Generate Images"
5. Monitor progress in the progress section
6. View generated images in the preview area
7. Images are automatically saved to your output directory

### Editing Images
1. Switch to the "✏️ Edit Images" tab
2. **Select an image to edit**:
   - **Upload from file**: Click "📁 Choose Image File" to upload an image from your computer
   - **Select from gallery**: Click "🖼️ Select from Gallery" to choose from previously generated images
   - **Edit from generation tab**: Click the "✏️ Edit" button on any generated image
3. **Enter editing instructions**: Describe what you want to change in natural language
   - Examples: "Add a sunset background", "Change the sky to purple", "Remove the background"
4. **Adjust editing parameters**:
   - **Seed**: For reproducible editing results
   - **Guidance Scale**: How closely to follow your editing instructions
   - **Number of Images**: Generate multiple edited versions
   - **Safety Tolerance**: Content filtering level (default: 6 for maximum creativity)
   - **Output Format**: PNG (recommended for editing) or JPEG
5. Click "✏️ Edit Image"
6. Monitor editing progress
7. View edited images in the preview area
8. **Additional options**:
   - **Open**: View the image in your default image viewer
   - **Export**: Save the image to a custom location
   - **Edit Again**: Use the edited image as input for another edit

### Quick Prompts
Use the quick prompt buttons for inspiration:

**Generation prompts**:
- "A majestic landscape at sunset"
- "Portrait of a wise old wizard"
- "Futuristic city skyline"
- "Abstract art with vibrant colors"

**Editing prompts**:
- "Change the background to a sunset"
- "Add a cat to the scene"
- "Make it look like a painting"
- "Remove the background"

## File Structure

```
flux-python/
├── main.py                 # Main application entry point
├── modern_gui.py          # Modern GUI with generation and editing tabs
├── generator.py           # FLUX API client for generation and editing
├── settings.py            # Configuration management with editing support
├── image_manager.py       # Image handling, caching, and editing support
├── logger.py              # Logging and error handling
├── test_application.py    # Test suite for generation features
├── test_image_editing.py  # Test suite for editing features
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── flux_config.json      # Configuration file (created automatically)
├── output/               # Generated and edited images directory
│   ├── thumbnails/       # Image thumbnails
│   └── prompts/          # Saved prompts and editing instructions
└── flux_generator.log    # Application log file
```

## Configuration

The application uses a JSON configuration file (`flux_config.json`) that is created automatically on first run. You can also manually edit this file:

```json
{
    "api_key": "your-fal-api-key",
    "output_directory": "output",
    "model_endpoint": "fal-ai/flux-pro/kontext/text-to-image",
    "guidance_scale": 3.5,
    "num_images": 1,
    "safety_tolerance": "2",
    "output_format": "jpeg",
    "aspect_ratio": "1:1",
    "window_width": 1200,
    "window_height": 800,
    "auto_open_images": true,
    "auto_save_settings": true,
    "editing_guidance_scale": 3.5,
    "editing_num_images": 1,
    "editing_safety_tolerance": "6",
    "editing_output_format": "png",
    "max_upload_size_mb": 50,
    "enable_editing_history": true
}
```

## Command Line Options

```bash
python main.py [options]

Options:
  -h, --help            Show help message
  -c, --config CONFIG   Path to configuration file
  -d, --debug           Enable debug mode with console logging
  -v, --version         Show version information
```

Examples:
```bash
python main.py                    # Run with default settings
python main.py --debug            # Run with debug logging
python main.py --config my.json   # Use custom config file
```

## Testing

Run the test suites to validate all components:

**Test generation functionality**:
```bash
python test_application.py
```

**Test image editing functionality**:
```bash
python test_image_editing.py
```

The test suites validate:
- Module imports and dependencies
- Configuration management (generation and editing)
- Request creation and validation
- Image manager functionality
- GUI component availability
- API client capabilities
- Workflow simulation
- Logging system

## Troubleshooting

### Common Issues

**"API Key Required" Error**
- Ensure you have entered a valid FAL API key in the settings
- Check that your API key has sufficient credits

**"Network Error"**
- Check your internet connection
- Verify firewall settings allow the application to access the internet
- Try disabling VPN if active

**"File Permission Error"**
- Ensure the application has write permissions to the output directory
- Try running as administrator on Windows
- Check available disk space

**GUI Not Appearing**
- Ensure tkinter is installed: `python -m tkinter`
- Update your graphics drivers
- Try running with `--debug` flag for more information

### Log Files
Check the log file (`flux_generator.log`) for detailed error information and debugging.

## API Information

This application uses the FLUX.1 Kontext Pro API endpoints:

**Text-to-Image Generation**:
- **Endpoint**: `fal-ai/flux-pro/kontext/text-to-image`
- **Documentation**: [FLUX.1 Kontext Pro Text-to-Image](https://fal.ai/models/fal-ai/flux-pro/kontext/text-to-image)

**Image Editing**:
- **Endpoint**: `fal-ai/flux-pro/kontext`
- **Documentation**: [FLUX.1 Kontext Pro Image Editing](https://fal.ai/models/fal-ai/flux-pro/kontext)

**Requirements**:
- **API Key**: Required from [fal.ai](https://fal.ai)
- **Supported Image Formats**: JPEG, PNG, WebP, BMP, TIFF (for upload)
- **Maximum Upload Size**: 50MB (configurable)

## License

This project is provided as-is for educational and personal use. Please respect the FLUX API terms of service and usage guidelines.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the log files for error details
3. Ensure all dependencies are properly installed
4. Verify your API key is valid and has credits

## Version History

**v1.1** - Image Editing Update
- **NEW**: Complete image editing capabilities using FLUX.1 Kontext Pro editing model
- **NEW**: Upload images from computer for editing
- **NEW**: Edit generated images directly from the gallery
- **NEW**: Natural language editing prompts with quick suggestions
- **NEW**: Multiple editing iterations and editing history tracking
- **NEW**: Tabbed interface for generation and editing modes
- **NEW**: Comprehensive editing test suite
- Enhanced image management with editing support
- Updated configuration system with editing parameters
- Improved GUI with editing controls and preview

**v1.0** - Initial release
- Complete rewrite of the original application
- Modern GUI with comprehensive parameter controls
- FLUX.1 Kontext Pro text-to-image API integration
- Advanced image management and caching
- Robust error handling and logging
- Comprehensive test suite
