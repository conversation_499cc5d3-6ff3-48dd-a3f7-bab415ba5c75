"""
Configuration Management System for FLUX.1 Kontext Pro Image Generator
Provides robust configuration handling with validation and environment variable support.
"""

import json
import os
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path

from generator import SafetyTolerance, OutputFormat, AspectRatio


@dataclass
class FluxKontextConfig:
    """Configuration class for FLUX.1 Kontext Pro parameters."""
    # API Configuration
    api_key: str = ""
    model_endpoint: str = "fal-ai/flux-pro/kontext/text-to-image"

    # Generation Parameters
    prompt: str = ""
    seed: Optional[int] = None
    guidance_scale: float = 3.5
    num_images: int = 1
    safety_tolerance: str = "6"  # SafetyTolerance enum value (maximum permissive)
    output_format: str = "png"  # OutputFormat enum value (PNG default)
    aspect_ratio: str = "1:1"    # AspectRatio enum value
    sync_mode: bool = False

    # Application Settings
    output_directory: str = "output"
    image_format: str = "jpg"  # Local file format
    retry_attempts: int = 3
    auto_open_images: bool = True
    save_prompts: bool = True

    # GUI Settings
    window_width: int = 1200
    window_height: int = 800
    theme: str = "modern"
    auto_save_settings: bool = True

    # Image Editing Settings
    editing_guidance_scale: float = 3.5
    editing_num_images: int = 1
    editing_safety_tolerance: str = "6"  # Maximum permissive for editing
    editing_output_format: str = "png"  # PNG default for editing
    max_upload_size_mb: int = 50  # Maximum upload file size
    enable_editing_history: bool = True  # Track editing relationships

    def validate(self) -> None:
        """Validate configuration parameters."""
        # Validate guidance scale
        if not 0 <= self.guidance_scale <= 20:
            raise ValueError("Guidance scale must be between 0 and 20")

        # Validate number of images
        if not 1 <= self.num_images <= 10:
            raise ValueError("Number of images must be between 1 and 10")

        # Validate seed
        if self.seed is not None and not 0 <= self.seed <= 2**32 - 1:
            raise ValueError("Seed must be between 0 and 2^32 - 1")

        # Validate enum values
        try:
            SafetyTolerance(self.safety_tolerance)
        except ValueError:
            raise ValueError(f"Invalid safety tolerance: {self.safety_tolerance}")

        try:
            OutputFormat(self.output_format)
        except ValueError:
            raise ValueError(f"Invalid output format: {self.output_format}")

        try:
            AspectRatio(self.aspect_ratio)
        except ValueError:
            raise ValueError(f"Invalid aspect ratio: {self.aspect_ratio}")

        # Validate retry attempts
        if not 1 <= self.retry_attempts <= 10:
            raise ValueError("Retry attempts must be between 1 and 10")

        # Validate editing parameters
        if not 0 <= self.editing_guidance_scale <= 20:
            raise ValueError("Editing guidance scale must be between 0 and 20")

        if not 1 <= self.editing_num_images <= 10:
            raise ValueError("Editing number of images must be between 1 and 10")

        try:
            SafetyTolerance(self.editing_safety_tolerance)
        except ValueError:
            raise ValueError(f"Invalid editing safety tolerance: {self.editing_safety_tolerance}")

        try:
            OutputFormat(self.editing_output_format)
        except ValueError:
            raise ValueError(f"Invalid editing output format: {self.editing_output_format}")

        if not 1 <= self.max_upload_size_mb <= 500:
            raise ValueError("Max upload size must be between 1 and 500 MB")


class ConfigManager:
    """
    Configuration manager for the FLUX.1 Kontext Pro Image Generator.
    Handles loading, saving, validation, and environment variable management.
    """

    DEFAULT_CONFIG_PATH = "flux_config.json"
    LEGACY_CONFIG_PATH = "config.json"

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_path: Path to the configuration file. If None, uses default.
        """
        self.config_path = Path(config_path or self.DEFAULT_CONFIG_PATH)
        self.config = FluxKontextConfig()
        self._load_config()

    def _load_config(self) -> None:
        """Load configuration from file or create default."""
        # Try to load from new config file first
        if self.config_path.exists():
            self._load_from_file(self.config_path)
        # Check for legacy config file
        elif Path(self.LEGACY_CONFIG_PATH).exists():
            self._migrate_legacy_config()
        else:
            # Create default config
            self._create_default_config()

        # Set environment variables
        self._set_environment_variables()

    def _load_from_file(self, file_path: Path) -> None:
        """Load configuration from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Update config with loaded data
            for key, value in data.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)

            # Validate the loaded configuration
            self.config.validate()

        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error loading config from {file_path}: {e}")
            print("Using default configuration.")
            self.config = FluxKontextConfig()
        except Exception as e:
            print(f"Unexpected error loading config: {e}")
            self.config = FluxKontextConfig()

    def _migrate_legacy_config(self) -> None:
        """Migrate from legacy config.json format."""
        try:
            with open(self.LEGACY_CONFIG_PATH, 'r', encoding='utf-8') as f:
                legacy_data = json.load(f)

            print("Migrating from legacy configuration...")

            # Map legacy settings to new format
            self.config.api_key = legacy_data.get("api_key", "")
            self.config.output_directory = legacy_data.get("output_directory", "output")
            self.config.image_format = legacy_data.get("image_format", "jpg")
            self.config.retry_attempts = legacy_data.get("retry_attempts", 3)

            # Map legacy default_settings
            if "default_settings" in legacy_data:
                legacy_settings = legacy_data["default_settings"]
                self.config.num_images = legacy_settings.get("num_images", 1)
                self.config.seed = legacy_settings.get("seed")

                # Convert legacy model-specific settings
                if legacy_settings.get("model") == "fal-ai/flux-pro/kontext/text-to-image":
                    self.config.model_endpoint = "fal-ai/flux-pro/kontext/text-to-image"

            # Save the migrated config
            self.save_config()
            print("Legacy configuration migrated successfully!")

        except Exception as e:
            print(f"Error migrating legacy config: {e}")
            self._create_default_config()

    def _create_default_config(self) -> None:
        """Create and save default configuration."""
        self.config = FluxKontextConfig()
        self.save_config()
        print("Created default configuration.")

    def _set_environment_variables(self) -> None:
        """Set environment variables from configuration."""
        if self.config.api_key:
            os.environ["FAL_KEY"] = self.config.api_key

    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            # Validate before saving
            self.config.validate()

            # Convert to dictionary
            config_dict = asdict(self.config)

            # Save to file
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)

            # Update environment variables
            self._set_environment_variables()

        except Exception as e:
            print(f"Error saving configuration: {e}")

    def get_config(self) -> FluxKontextConfig:
        """Get the current configuration."""
        return self.config

    def update_config(self, **kwargs) -> None:
        """
        Update configuration parameters.

        Args:
            **kwargs: Configuration parameters to update.
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)

        # Validate and save
        try:
            self.config.validate()
            if self.config.auto_save_settings:
                self.save_config()
        except ValueError as e:
            print(f"Configuration validation error: {e}")

    def ensure_output_directory(self) -> None:
        """Ensure the output directory exists."""
        output_path = Path(self.config.output_directory)
        output_path.mkdir(parents=True, exist_ok=True)

    def get_generation_params(self) -> Dict[str, Any]:
        """Get parameters formatted for image generation."""
        return {
            "prompt": self.config.prompt,
            "seed": self.config.seed,
            "guidance_scale": self.config.guidance_scale,
            "num_images": self.config.num_images,
            "safety_tolerance": self.config.safety_tolerance,
            "output_format": self.config.output_format,
            "aspect_ratio": self.config.aspect_ratio,
            "sync_mode": self.config.sync_mode
        }

    def get_editing_params(self) -> Dict[str, Any]:
        """Get parameters formatted for image editing."""
        return {
            "guidance_scale": self.config.editing_guidance_scale,
            "num_images": self.config.editing_num_images,
            "safety_tolerance": self.config.editing_safety_tolerance,
            "output_format": self.config.editing_output_format,
            "max_upload_size_mb": self.config.max_upload_size_mb,
            "enable_editing_history": self.config.enable_editing_history
        }

    @staticmethod
    def get_available_options() -> Dict[str, list]:
        """Get available options for configuration parameters."""
        return {
            "safety_tolerance": [e.value for e in SafetyTolerance],
            "output_format": [e.value for e in OutputFormat],
            "aspect_ratio": [e.value for e in AspectRatio],
            "themes": ["modern", "dark", "light", "classic"]
        }


# Legacy compatibility functions for backward compatibility
def load_config():
    """Legacy function for backward compatibility."""
    config_manager = ConfigManager()
    config = config_manager.get_config()

    # Convert to legacy format
    return {
        "output_directory": config.output_directory,
        "image_format": config.image_format,
        "retry_attempts": config.retry_attempts,
        "api_key": config.api_key,
        "default_settings": {
            "model": config.model_endpoint,
            "num_images": config.num_images,
            "seed": config.seed,
            "guidance_scale": config.guidance_scale,
            "safety_tolerance": config.safety_tolerance,
            "output_format": config.output_format,
            "aspect_ratio": config.aspect_ratio
        }
    }


def save_config(config):
    """Legacy function for backward compatibility."""
    config_manager = ConfigManager()

    # Update from legacy format
    config_manager.update_config(
        output_directory=config.get("output_directory", "output"),
        image_format=config.get("image_format", "jpg"),
        retry_attempts=config.get("retry_attempts", 3),
        api_key=config.get("api_key", "")
    )

    if "default_settings" in config:
        settings = config["default_settings"]
        config_manager.update_config(
            model_endpoint=settings.get("model", "fal-ai/flux-pro/kontext/text-to-image"),
            num_images=settings.get("num_images", 1),
            seed=settings.get("seed"),
            guidance_scale=settings.get("guidance_scale", 3.5),
            safety_tolerance=settings.get("safety_tolerance", "2"),
            output_format=settings.get("output_format", "jpeg"),
            aspect_ratio=settings.get("aspect_ratio", "1:1")
        )

    config_manager.save_config()


def get_default_config():
    """Legacy function for backward compatibility."""
    config = FluxKontextConfig()
    return {
        "output_directory": config.output_directory,
        "image_format": config.image_format,
        "retry_attempts": config.retry_attempts,
        "api_key": config.api_key,
        "default_settings": {
            "model": config.model_endpoint,
            "num_images": config.num_images,
            "seed": config.seed,
            "guidance_scale": config.guidance_scale,
            "safety_tolerance": config.safety_tolerance,
            "output_format": config.output_format,
            "aspect_ratio": config.aspect_ratio
        }
    }


def ensure_output_directory(config):
    """Legacy function for backward compatibility."""
    output_path = Path(config.get("output_directory", "output"))
    output_path.mkdir(parents=True, exist_ok=True)
