#!/usr/bin/env python3
"""
Complete workflow test script for FLUX image generation and editing.
This script will:
1. Generate a random image
2. Attempt to edit it
3. Log all operations and errors
4. Test drag-and-drop functionality
"""

import os
import sys
import logging
import traceback
import threading
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_complete_workflow.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_imports():
    """Test all required imports."""
    logger.info("Testing imports...")
    try:
        import tkinter as tk
        logger.info("✓ tkinter imported successfully")
        
        try:
            import tkinterdnd2 as TkinterDnD
            logger.info("✓ tkinterdnd2 imported successfully")
        except ImportError as e:
            logger.warning(f"⚠ tkinterdnd2 not available: {e}")
            logger.warning("Drag-and-drop functionality will not work")
        
        from generator import FluxClient, GenerationRequest, EditingRequest
        logger.info("✓ Generator classes imported successfully")
        
        from modern_gui import ModernFluxGUI
        logger.info("✓ ModernFluxGUI imported successfully")
        
        return True
    except Exception as e:
        logger.error(f"✗ Import failed: {e}")
        logger.error(traceback.format_exc())
        return False

def test_flux_client():
    """Test FLUX client initialization."""
    logger.info("Testing FLUX client initialization...")
    try:
        from generator import FluxClient
        client = FluxClient()
        logger.info("✓ FLUX client initialized successfully")
        return client
    except Exception as e:
        logger.error(f"✗ FLUX client initialization failed: {e}")
        logger.error(traceback.format_exc())
        return None

def generate_test_image(client):
    """Generate a test image."""
    logger.info("Generating test image...")
    try:
        from generator import GenerationRequest, SafetyTolerance, OutputFormat
        import random
        
        # Create a simple generation request
        request = GenerationRequest(
            prompt="A beautiful sunset over mountains, digital art",
            width=512,
            height=512,
            num_images=1,
            seed=random.randint(1, 1000000),
            guidance_scale=7.5,
            num_inference_steps=28,
            safety_tolerance=SafetyTolerance.STRICT,
            output_format=OutputFormat.PNG
        )
        
        logger.info(f"Generation request: {request.prompt}")
        logger.info(f"Seed: {request.seed}")
        
        # Generate image
        result = client.generate_image(request)
        logger.info(f"✓ Image generated successfully: {result}")
        return result
        
    except Exception as e:
        logger.error(f"✗ Image generation failed: {e}")
        logger.error(traceback.format_exc())
        return None

def test_gui_initialization():
    """Test GUI initialization."""
    logger.info("Testing GUI initialization...")
    try:
        import tkinter as tk
        from modern_gui import ModernFluxGUI
        
        root = tk.Tk()
        root.withdraw()  # Hide the window for testing
        
        app = ModernFluxGUI(root)
        logger.info("✓ GUI initialized successfully")
        
        # Test drag-and-drop availability
        if hasattr(app, 'DRAG_DROP_AVAILABLE'):
            logger.info(f"Drag-and-drop available: {app.DRAG_DROP_AVAILABLE}")
        else:
            logger.warning("DRAG_DROP_AVAILABLE attribute not found")
        
        return app, root
        
    except Exception as e:
        logger.error(f"✗ GUI initialization failed: {e}")
        logger.error(traceback.format_exc())
        return None, None

def test_image_upload_and_edit(app, image_path):
    """Test image upload and editing workflow."""
    logger.info(f"Testing image upload and edit workflow with: {image_path}")
    
    if not app:
        logger.error("No app instance available")
        return False
    
    try:
        # Test if image file exists
        if not os.path.exists(image_path):
            logger.error(f"Image file not found: {image_path}")
            return False
        
        logger.info(f"Image file exists: {image_path}")
        
        # Test load_dropped_image method
        if hasattr(app, 'load_dropped_image'):
            logger.info("Testing load_dropped_image method...")
            app.load_dropped_image(image_path)
            logger.info("✓ load_dropped_image called successfully")
            
            # Check if image was loaded
            if hasattr(app, 'current_editing_image_path'):
                logger.info(f"Current editing image path: {app.current_editing_image_path}")
            
            # Check edit button state
            if hasattr(app, 'edit_btn'):
                edit_btn_state = app.edit_btn['state']
                logger.info(f"Edit button state: {edit_btn_state}")
                
                if edit_btn_state == 'normal':
                    logger.info("✓ Edit button is enabled")
                    return True
                else:
                    logger.warning("⚠ Edit button is not enabled")
                    
                    # Check what's missing
                    if hasattr(app, 'current_editing_image_url'):
                        logger.info(f"Current editing image URL: {app.current_editing_image_url}")
                    else:
                        logger.warning("current_editing_image_url not found")
                        
            else:
                logger.error("edit_btn not found in app")
        else:
            logger.error("load_dropped_image method not found")
            
    except Exception as e:
        logger.error(f"✗ Image upload/edit test failed: {e}")
        logger.error(traceback.format_exc())
        return False
    
    return False

def test_drag_drop_simulation(app):
    """Test drag-and-drop simulation."""
    logger.info("Testing drag-and-drop simulation...")
    
    if not app:
        logger.error("No app instance available")
        return False
    
    try:
        # Check if drag-and-drop methods exist
        methods_to_check = ['handle_drop', 'handle_widget_drop', 'load_dropped_image']
        
        for method_name in methods_to_check:
            if hasattr(app, method_name):
                logger.info(f"✓ {method_name} method found")
            else:
                logger.warning(f"⚠ {method_name} method not found")
        
        # Check DRAG_DROP_AVAILABLE
        if hasattr(app, 'DRAG_DROP_AVAILABLE'):
            logger.info(f"DRAG_DROP_AVAILABLE: {app.DRAG_DROP_AVAILABLE}")
            if not app.DRAG_DROP_AVAILABLE:
                logger.warning("Drag-and-drop is not available - tkinterdnd2 missing")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Drag-drop simulation failed: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Main test function."""
    logger.info("=" * 60)
    logger.info("STARTING COMPLETE WORKFLOW TEST")
    logger.info("=" * 60)
    
    # Test 1: Imports
    if not test_imports():
        logger.error("Import test failed - stopping")
        return
    
    # Test 2: FLUX Client
    client = test_flux_client()
    if not client:
        logger.error("FLUX client test failed - stopping")
        return
    
    # Test 3: Generate image
    image_result = generate_test_image(client)
    if not image_result:
        logger.error("Image generation failed - stopping")
        return
    
    # Find the generated image file
    output_dir = Path("output")
    if output_dir.exists():
        image_files = list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpg"))
        if image_files:
            # Use the most recent image
            latest_image = max(image_files, key=os.path.getctime)
            logger.info(f"Using generated image: {latest_image}")
        else:
            logger.error("No image files found in output directory")
            return
    else:
        logger.error("Output directory not found")
        return
    
    # Test 4: GUI initialization
    app, root = test_gui_initialization()
    if not app or not root:
        logger.error("GUI initialization failed - stopping")
        return
    
    # Test 5: Drag-drop simulation
    test_drag_drop_simulation(app)
    
    # Test 6: Image upload and edit workflow
    test_image_upload_and_edit(app, str(latest_image))
    
    # Clean up
    try:
        root.destroy()
        logger.info("✓ GUI cleaned up successfully")
    except Exception as e:
        logger.warning(f"GUI cleanup warning: {e}")
    
    logger.info("=" * 60)
    logger.info("COMPLETE WORKFLOW TEST FINISHED")
    logger.info("=" * 60)
    logger.info("Check test_complete_workflow.log for detailed logs")

if __name__ == "__main__":
    main()